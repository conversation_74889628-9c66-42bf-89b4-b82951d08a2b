package com.Pro.Pro.repository;

import com.Pro.Pro.model.Assignment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssignmentRepository extends JpaRepository<Assignment, String> {

    Optional<Assignment> findByLessonId(String lessonId);

    boolean existsByLessonId(String lessonId);

    @Query(value = """
        SELECT a.id FROM assignments a 
        WHERE a.lesson_id = :lessonId
        """, nativeQuery = true)
    Optional<String> findAssignmentIdByLessonId(@Param("lessonId") String lessonId);

    @Query(value = """
        SELECT COUNT(sa) > 0 FROM student_assignments sa 
        WHERE sa.assignment_id = :assignmentId 
        AND sa.submission_date IS NOT NULL
        """, nativeQuery = true)
    boolean hasSubmissions(@Param("assignmentId") String assignmentId);

    @Query("SELECT COUNT(a) FROM Assignment a WHERE a.lesson.course.instructor.id = :instructorId")
    long countByInstructorId(@Param("instructorId") String instructorId);

    // Additional methods for improved functionality
    boolean existsByLessonIdAndTitle(String lessonId, String title);

    List<Assignment> findByLessonIdOrderByCreatedAtDesc(String lessonId);

    @Query("SELECT a FROM Assignment a WHERE a.lesson.id = :lessonId ORDER BY a.dueDate ASC")
    List<Assignment> findByLessonIdOrderByDueDateAsc(@Param("lessonId") String lessonId);
}
