package com.Pro.Pro.repository;

import com.Pro.Pro.model.FawryPayment;
import com.Pro.Pro.model.PaymentStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface FawryPaymentRepository extends JpaRepository<FawryPayment, String> {

    Optional<FawryPayment> findByStudent_IdAndLesson_Id(String studentId, String lessonId);

    // Additional methods for improved functionality
    Optional<FawryPayment> findByReferenceNumber(String referenceNumber);

    List<FawryPayment> findByStudentIdOrderByCreatedAtDesc(String studentId);

    Page<FawryPayment> findByStudentIdOrderByCreatedAtDesc(String studentId, Pageable pageable);

    List<FawryPayment> findByStatus(PaymentStatus status);

    @Query("SELECT fp FROM FawryPayment fp WHERE fp.student.id = :studentId AND fp.status = :status")
    List<FawryPayment> findByStudentIdAndStatus(@Param("studentId") String studentId, @Param("status") PaymentStatus status);

    @Query("SELECT COUNT(fp) FROM FawryPayment fp WHERE fp.status = :status AND fp.createdAt >= :fromDate")
    long countByStatusAndCreatedAtAfter(@Param("status") PaymentStatus status, @Param("fromDate") Date fromDate);

    @Query("SELECT SUM(fp.amount) FROM FawryPayment fp WHERE fp.status = :status AND fp.createdAt >= :fromDate")
    BigDecimal sumAmountByStatusAndCreatedAtAfter(@Param("status") PaymentStatus status, @Param("fromDate") Date fromDate);

    boolean existsByReferenceNumber(String referenceNumber);

    @Query("SELECT fp FROM FawryPayment fp WHERE fp.lesson.id = :lessonId ORDER BY fp.createdAt DESC")
    List<FawryPayment> findByLessonIdOrderByCreatedAtDesc(@Param("lessonId") String lessonId);
}
