package com.Pro.Pro.repository;

import com.Pro.Pro.model.FawryPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface FawryPaymentRepository extends JpaRepository<FawryPayment, String> {
    Optional<FawryPayment> findByStudent_IdAndLesson_Id(String studentId, String lessonId);

}
