package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.LessonAccessRequest;
import com.Pro.Pro.dto.response.PaymentResponse;
import com.Pro.Pro.exception.ConflictException;
import com.Pro.Pro.exception.ResourceNotFoundException;
import org.apache.coyote.BadRequestException;

import java.time.LocalDate;
import java.util.UUID;

public interface PaymentService {
    /**
     * Grants lesson access either via access code or payment
     * @param request Contains lessonId and either accessCode or paymentMethod
     * @param studentId Authenticated student ID
     * @return PaymentResponse with access details
     * @throws BadRequestException for invalid requests
     * @throws ResourceNotFoundException if resources not found
     * @throws ConflictException if access already exists
     */
    PaymentResponse grantLessonAccess(LessonAccessRequest request, String studentId)
            throws BadRequestException, ResourceNotFoundException, ConflictException;

    /**
     * Checks if student has valid access to a lesson
     * @param studentId Student ID to check
     * @param lessonId Lesson ID to check
     * @return true if access is valid, false otherwise
     */
    boolean hasValidAccess(String studentId, String lessonId);

    /**
     * Gets the access expiry date for a student's lesson access
     * @param studentId Student ID
     * @param lessonId Lesson ID
     * @return LocalDate of access expiry
     * @throws ResourceNotFoundException if access record not found
     */
    LocalDate getAccessExpiryDate(String studentId, String lessonId)
            throws ResourceNotFoundException;

    /**
     * Gets the payment status for a lesson access
     * @param studentId Student ID
     * @param lessonId Lesson ID
     * @return Payment status or null if no payment exists
     */
    String getPaymentStatus(String studentId, String lessonId);
}