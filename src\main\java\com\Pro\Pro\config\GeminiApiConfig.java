//package com.Pro.Pro.config;
//
//import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
//import io.swagger.v3.oas.annotations.security.SecurityScheme;
//import io.swagger.v3.oas.models.OpenAPI;
//import io.swagger.v3.oas.models.info.Info;
//import io.swagger.v3.oas.models.security.SecurityRequirement;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//@SecurityScheme(
//        name = "JWT",
//        type = SecuritySchemeType.HTTP,
//        bearerFormat = "JWT",
//        scheme = "bearer"
//)
//public class GeminiApiConfig {
//
//    @Bean
//    public GeminiAPI customOpenAPI() {
//        return new GeminiAPI()
//                .info(new Info()
//                        .title("Pro")
//                        .version("1.0")
//                        .description("Educational Platform Management"))
//                .addSecurityItem(new SecurityRequirement().addList("JWT"));
//    }
//}