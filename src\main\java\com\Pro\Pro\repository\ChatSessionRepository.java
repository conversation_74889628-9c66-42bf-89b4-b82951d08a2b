package com.Pro.Pro.repository;

import com.Pro.Pro.model.ChatSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ChatSessionRepository extends JpaRepository<ChatSession, Long> {
    Optional<ChatSession> findBySessionToken(String sessionToken);
    List<ChatSession> findByUserId(String userId);
    Optional<ChatSession>  findBySessionTokenAndUserId(String sessionId, String userId);
}