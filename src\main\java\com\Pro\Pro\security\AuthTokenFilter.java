package com.Pro.Pro.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.authentication.session.SessionAuthenticationException;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Comparator;
import java.util.List;

public class AuthTokenFilter extends OncePerRequestFilter {
    private static final Logger logger = LoggerFactory.getLogger(AuthTokenFilter.class);

    private final JwtUtils jwtUtils;
    private final UserDetailsServiceImpl userDetailsService;
    private final SessionRegistry sessionRegistry;
    private static final String AUTH_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String SESSION_EXPIRED_MSG = "Session expired - please login again";
    private static final String INVALID_TOKEN_MSG = "Invalid JWT token";
    private static final String USER_NOT_FOUND_MSG = "User not found";
    private static final String COOKIE_NAME = "jwt";

    public AuthTokenFilter(JwtUtils jwtUtils,
                           UserDetailsServiceImpl userDetailsService,
                           SessionRegistry sessionRegistry) {
        this.jwtUtils = jwtUtils;
        this.userDetailsService = userDetailsService;
        this.sessionRegistry = sessionRegistry;
    }

    // In AuthTokenFilter.java
    private String parseJwt(HttpServletRequest request) {
        // Check Authorization header first
        String headerAuth = request.getHeader(AUTH_HEADER);
        if (StringUtils.hasText(headerAuth) && headerAuth.startsWith(BEARER_PREFIX)) {
            return headerAuth.substring(BEARER_PREFIX.length());
        }

        // Then check cookies
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (COOKIE_NAME.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }

        return null;
    }
//
//    private void setJwtCookie(HttpServletResponse response, String jwt, boolean secure) {
//        String cookieValue = String.format(
//                "%s=%s; Path=/; HttpOnly; SameSite=Strict; Max-Age=%d%s",
//                COOKIE_NAME,
//                jwt,
//                jwtUtils.getJwtExpirationMs() / 1000,
//                secure ? "; Secure" : ""
//        );
//        response.setHeader("Set-Cookie", cookieValue);
//    }
//
//    private void removeJwtCookie(HttpServletResponse response, boolean secure) {
//        String cookieValue = String.format(
//                "%s=; Path=/; HttpOnly; SameSite=Strict; Max-Age=0%s",
//                COOKIE_NAME,
//                secure ? "; Secure" : ""
//        );
//        response.setHeader("Set-Cookie", cookieValue);
//    }

    // In AuthTokenFilter.java - modify the successful authentication part:
    private void handleSuccessfulAuthentication(HttpServletRequest request,
                                                HttpServletResponse response,
                                                Authentication authentication) throws IOException, ServletException {
        try {
            String jwt = jwtUtils.generateJwtToken(authentication);

            // Create cookie with strict attributes
            String cookieValue = String.format(
                    "jwt=%s; Path=/; HttpOnly; SameSite=Strict; Max-Age=%d%s",
                    jwt,
                    jwtUtils.getJwtExpirationMs() / 1000,
                    request.isSecure() ? "; Secure" : ""
            );

            response.setHeader("Set-Cookie", cookieValue);

        } catch (Exception e) {
            logger.error("Failed to create authentication cookie", e);
            throw new ServletException("Authentication failed", e);
        }
    }
    private String buildCookieAttributes(HttpServletRequest request) {
        return String.format(
                "HttpOnly; Secure=%b; Path=/; SameSite=Strict; Max-Age=%d",
                request.isSecure(),
                jwtUtils.getJwtExpirationMs() / 1000
        );
    }

    private boolean isSessionValid(HttpServletRequest request, String username) {
        HttpSession session = request.getSession(false);
        if (session == null) return true;

        // Get all valid sessions for this user
        List<SessionInformation> sessions = sessionRegistry.getAllSessions(username, false);

        // Check if current session is the most recent one
        return sessions.stream()
                .max(Comparator.comparing(SessionInformation::getLastRequest))
                .map(latest -> latest.getSessionId().equals(session.getId()))
                .orElse(true);
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain)
            throws ServletException, IOException {
        try {
            String jwt = parseJwt(request);

            // If this is a logout request, skip authentication
            if (request.getRequestURI().contains("/api/auth/signout")) {
                filterChain.doFilter(request, response);
                return;
            }

            if (jwt != null && jwtUtils.validateJwtToken(jwt)) {
                String username = jwtUtils.getUserNameFromJwtToken(jwt);
                UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(
                                userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        } catch (Exception e) {
            logger.error("Cannot set user authentication: {}", e.getMessage());
        }

        filterChain.doFilter(request, response);
    }

    private boolean isSessionManagementEnabled(HttpServletRequest request) {
        // Check if this is a login request - we don't want to validate session for login
        if (request.getRequestURI().contains("/api/auth/signin")) {
            return false;
        }
        // Add other paths where session validation shouldn't happen
        return true;
    }


    private void sendErrorResponse(HttpServletResponse response, int status, String message) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json");
        response.getWriter().write(String.format("{\"error\": \"%s\"}", message));
    }
}