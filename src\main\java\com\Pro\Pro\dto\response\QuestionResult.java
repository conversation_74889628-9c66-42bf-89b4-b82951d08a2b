package com.Pro.Pro.dto.response;

import java.math.BigDecimal;

public class QuestionResult {
    private final BigDecimal maxPoints;
    private final BigDecimal pointsEarned;
    private final boolean isCorrect;
    private final String feedback;
    private final String submittedAnswer;

    public QuestionResult(BigDecimal maxPoints, BigDecimal pointsEarned,
                          boolean isCorrect, String feedback, String submittedAnswer) {
        this.maxPoints = maxPoints;
        this.pointsEarned = pointsEarned;
        this.isCorrect = isCorrect;
        this.feedback = feedback;
        this.submittedAnswer = submittedAnswer;
    }

    // Getters for all fields
    public BigDecimal getMaxPoints() {
        return maxPoints;
    }

    public BigDecimal getPointsEarned() {
        return pointsEarned;
    }

    public boolean isCorrect() {
        return isCorrect;
    }

    public String getFeedback() {
        return feedback;
    }

    public String getSubmittedAnswer() {
        return submittedAnswer;
    }
}