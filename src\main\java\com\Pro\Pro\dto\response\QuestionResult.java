package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;

@Data
@Builder
public class QuestionResult {
    private final BigDecimal maxPoints;
    private final BigDecimal pointsEarned;
    private final boolean isCorrect;
    private final String feedback;
    private final String submittedAnswer;

    public QuestionResult(BigDecimal maxPoints, BigDecimal pointsEarned,
                          boolean isCorrect, String feedback, String submittedAnswer) {
        this.maxPoints = maxPoints;
        this.pointsEarned = pointsEarned;
        this.isCorrect = isCorrect;
        this.feedback = feedback;
        this.submittedAnswer = submittedAnswer;
    }
}