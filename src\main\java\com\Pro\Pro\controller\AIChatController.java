package com.Pro.Pro.controller;

import com.Pro.Pro.dto.request.ChatRequest;
import com.Pro.Pro.security.CurrentUser;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.AIChatService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/chat")
@RequiredArgsConstructor
@EnableMethodSecurity
public class AIChatController {
@Autowired
    private final AIChatService chatService;
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/start")
    public ResponseEntity<?> startNewSession(@CurrentUser UserDetailsImpl currentUser) {
        try {
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of("error", "You must be logged in to start a chat session."));
            }
            return ResponseEntity.ok(chatService.startNewSession(currentUser.getId()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "An error occurred while starting a chat session.", "details", e.getMessage()));
        }
    }
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/send")
    public ResponseEntity<?> sendMessage(
            @Valid @RequestBody ChatRequest request,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of("error", "You must be logged in to send a message."));
            }
            return ResponseEntity.ok(chatService.sendMessage(request, currentUser.getId()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "An error occurred while sending the message.", "details", e.getMessage()));
        }
    }
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/history/{sessionId}")
    public ResponseEntity<?> getChatHistory(
            @PathVariable String sessionId,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            if (currentUser == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of("error", "You must be logged in to view chat history."));
            }
            return ResponseEntity.ok(chatService.getChatHistory(sessionId, currentUser.getId()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "An error occurred while fetching chat history.", "details", e.getMessage()));
        }
    }
}
