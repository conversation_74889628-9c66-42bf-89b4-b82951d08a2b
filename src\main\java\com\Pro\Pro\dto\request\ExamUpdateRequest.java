package com.Pro.Pro.dto.request;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class ExamUpdateRequest {

    private String examId;
    private String lessonId;
    private String title;
    private BigDecimal passingScore;
    private List<ExamQuestionUpdateRequest> questions;

    public String getExamId() {
        return examId;
    }

    public void setExamId(String examId) {
        this.examId = examId;
    }

    public String getLessonId() {
        return lessonId;
    }

    public void setLessonId(String lessonId) {
        this.lessonId = lessonId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BigDecimal getPassingScore() {
        return passingScore;
    }

    public void setPassingScore(BigDecimal passingScore) {
        this.passingScore = passingScore;
    }

    public List<ExamQuestionUpdateRequest> getQuestions() {
        return questions;
    }

    public void setQuestions(List<ExamQuestionUpdateRequest> questions) {
        this.questions = questions;
    }
}
