package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.Pro.Pro.dto.request.ExamQuestionRequest;
import com.Pro.Pro.dto.request.ExamRequest;
import com.Pro.Pro.model.QuestionType;
import com.Pro.Pro.service.serviceImp.ExamServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class ExamServiceImplTest {

    @Test
    public void testMultipleChoiceQuestionCreation() {
        // Test that we can create a valid multiple choice exam request
        ExamQuestionRequest question = ExamQuestionRequest.builder()
                .questionText("Which are OOP languages?")
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("15.00"))
                .answers(Arrays.asList(
                        ExamAnswerRequest.builder().answerText("Java").correct(true).build(),
                        ExamAnswerRequest.builder().answerText("Python").correct(true).build(),
                        ExamAnswerRequest.builder().answerText("C").correct(false).build(),
                        ExamAnswerRequest.builder().answerText("Assembly").correct(false).build()
                ))
                .build();

        ExamRequest examRequest = ExamRequest.builder()
                .title("Programming Quiz")
                .passingScore(new BigDecimal("80.00"))
                .timeLimitMinutes(60)
                .questions(Arrays.asList(question))
                .build();

        // Verify the structure is correct
        assertNotNull(examRequest);
        assertEquals("Programming Quiz", examRequest.getTitle());
        assertEquals(1, examRequest.getQuestions().size());

        ExamQuestionRequest testQuestion = examRequest.getQuestions().get(0);
        assertEquals(4, testQuestion.getAnswers().size());

        // Count correct answers
        long correctCount = testQuestion.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();

        assertEquals(2, correctCount, "Should have 2 correct answers for multiple choice question");
    }

    @Test
    public void testSingleChoiceQuestionCreation() {
        ExamQuestionRequest question = ExamQuestionRequest.builder()
                .questionText("What is the capital of France?")
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(new BigDecimal("10.00"))
                .answers(Arrays.asList(
                        ExamAnswerRequest.builder().answerText("Paris").correct(true).build(),
                        ExamAnswerRequest.builder().answerText("London").correct(false).build(),
                        ExamAnswerRequest.builder().answerText("Berlin").correct(false).build(),
                        ExamAnswerRequest.builder().answerText("Madrid").correct(false).build()
                ))
                .build();

        ExamRequest examRequest = ExamRequest.builder()
                .title("Geography Quiz")
                .passingScore(new BigDecimal("70.00"))
                .timeLimitMinutes(30)
                .questions(Arrays.asList(question))
                .build();

        assertNotNull(examRequest);
        assertEquals(1, examRequest.getQuestions().size());

        ExamQuestionRequest testQuestion = examRequest.getQuestions().get(0);
        assertEquals(4, testQuestion.getAnswers().size());

        // Count correct answers (should be exactly 1 for single choice)
        long correctCount = testQuestion.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();

        assertEquals(1, correctCount, "Should have exactly 1 correct answer for single choice question");
    }

    @Test
    public void testTrueFalseQuestionCreation() {
        ExamQuestionRequest question = ExamQuestionRequest.builder()
                .questionText("Java is an object-oriented programming language.")
                .questionType(QuestionType.TRUE_FALSE)
                .points(new BigDecimal("5.00"))
                .answers(Arrays.asList(
                        ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                        ExamAnswerRequest.builder().answerText("false").correct(false).build()
                ))
                .build();

        ExamRequest examRequest = ExamRequest.builder()
                .title("Java Basics")
                .passingScore(new BigDecimal("60.00"))
                .timeLimitMinutes(15)
                .questions(Arrays.asList(question))
                .build();

        assertNotNull(examRequest);
        assertEquals(1, examRequest.getQuestions().size());

        ExamQuestionRequest testQuestion = examRequest.getQuestions().get(0);
        assertEquals(2, testQuestion.getAnswers().size());

        // Count correct answers (should be exactly 1 for true/false)
        long correctCount = testQuestion.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();

        assertEquals(1, correctCount, "Should have exactly 1 correct answer for true/false question");

        // Verify answers are "true" and "false"
        List<String> answerTexts = testQuestion.getAnswers().stream()
                .map(ExamAnswerRequest::getAnswerText)
                .collect(java.util.stream.Collectors.toList());

        assertTrue(answerTexts.contains("true"));
        assertTrue(answerTexts.contains("false"));
    }

    @Test
    public void testMixedQuestionTypesExam() {
        List<ExamQuestionRequest> questions = Arrays.asList(
                // Multiple choice question
                ExamQuestionRequest.builder()
                        .questionText("Which are programming languages?")
                        .questionType(QuestionType.MULTIPLE_CHOICE)
                        .points(new BigDecimal("20.00"))
                        .answers(Arrays.asList(
                                ExamAnswerRequest.builder().answerText("Java").correct(true).build(),
                                ExamAnswerRequest.builder().answerText("Python").correct(true).build(),
                                ExamAnswerRequest.builder().answerText("HTML").correct(false).build(),
                                ExamAnswerRequest.builder().answerText("CSS").correct(false).build()
                        ))
                        .build(),
                // Single choice question
                ExamQuestionRequest.builder()
                        .questionText("What does JVM stand for?")
                        .questionType(QuestionType.SINGLE_CHOICE)
                        .points(new BigDecimal("15.00"))
                        .answers(Arrays.asList(
                                ExamAnswerRequest.builder().answerText("Java Virtual Machine").correct(true).build(),
                                ExamAnswerRequest.builder().answerText("Java Variable Method").correct(false).build(),
                                ExamAnswerRequest.builder().answerText("Java Version Manager").correct(false).build()
                        ))
                        .build(),
                // True/False question
                ExamQuestionRequest.builder()
                        .questionText("Java is platform independent.")
                        .questionType(QuestionType.TRUE_FALSE)
                        .points(new BigDecimal("10.00"))
                        .answers(Arrays.asList(
                                ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                                ExamAnswerRequest.builder().answerText("false").correct(false).build()
                        ))
                        .build()
        );

        ExamRequest examRequest = ExamRequest.builder()
                .title("Comprehensive Programming Test")
                .passingScore(new BigDecimal("75.00"))
                .timeLimitMinutes(90)
                .questions(questions)
                .build();

        assertNotNull(examRequest);
        assertEquals(3, examRequest.getQuestions().size());

        // Verify each question type
        assertEquals(QuestionType.MULTIPLE_CHOICE, examRequest.getQuestions().get(0).getQuestionType());
        assertEquals(QuestionType.SINGLE_CHOICE, examRequest.getQuestions().get(1).getQuestionType());
        assertEquals(QuestionType.TRUE_FALSE, examRequest.getQuestions().get(2).getQuestionType());
    }
}
