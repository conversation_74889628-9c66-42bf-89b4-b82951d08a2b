package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.Pro.Pro.dto.request.ExamQuestionRequest;
import com.Pro.Pro.dto.request.ExamRequest;
import com.Pro.Pro.model.QuestionType;
import com.Pro.Pro.service.serviceImp.ExamServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class ExamServiceImplTest {

    @Test
    public void testExamAnswerRequestDeserialization() {
        // Test that ExamAnswerRequest properly deserializes the 'correct' field
        ExamAnswerRequest answer1 = ExamAnswerRequest.builder()
                .answerText("Java")
                .correct(true)
                .build();
        
        ExamAnswerRequest answer2 = ExamAnswerRequest.builder()
                .answerText("Assembly")
                .correct(false)
                .build();
        
        assertTrue(answer1.isCorrect());
        assertFalse(answer2.isCorrect());
        
        // Test that we can create a valid exam request
        ExamQuestionRequest question = ExamQuestionRequest.builder()
                .questionText("Which are OOP languages?")
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("15.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Java").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Python").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("C").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Assembly").correct(false).build()
                ))
                .build();
        
        ExamRequest examRequest = ExamRequest.builder()
                .title("Programming Quiz")
                .passingScore(new BigDecimal("80.00"))
                .timeLimitMinutes(60)
                .questions(Arrays.asList(question))
                .build();
        
        // Verify the structure is correct
        assertNotNull(examRequest);
        assertEquals("Programming Quiz", examRequest.getTitle());
        assertEquals(1, examRequest.getQuestions().size());
        
        ExamQuestionRequest testQuestion = examRequest.getQuestions().get(0);
        assertEquals(4, testQuestion.getAnswers().size());
        
        // Count correct answers
        long correctCount = testQuestion.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();
        
        assertEquals(2, correctCount, "Should have 2 correct answers for multiple choice question");
    }
}
