package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;
import java.util.Date;

@Data
@Builder
public class CourseResponse {

    private String id;
    private String name;
    private String description;
    private String photoUrl;
    private String instructorId;
    private String instructorName;
    private String instructorBio;
    private String instructorPhoto;
    private Date createdAt;
    private int lessonCount;

}