package com.Pro.Pro.repository;

import com.Pro.Pro.model.StudentAssignment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface StudentAssignmentRepository extends JpaRepository<StudentAssignment, String> {
    Optional<StudentAssignment> findByStudentIdAndAssignmentId(String studentId, String assignmentId);
    List<StudentAssignment> findByAssignmentId(String assignmentId);
    void deleteByAssignmentId(String assignmentId);

    boolean existsByAssignmentId(String assignmentId);
}