package com.Pro.Pro.repository;

import com.Pro.Pro.model.StudentAssignment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StudentAssignmentRepository extends JpaRepository<StudentAssignment, String> {

    Optional<StudentAssignment> findByStudentIdAndAssignmentId(String studentId, String assignmentId);

    Page<StudentAssignment> findByAssignmentId(String assignmentId, Pageable pageable);

    List<StudentAssignment> findByAssignmentId(String assignmentId);

    void deleteByAssignmentId(String assignmentId);

    boolean existsByAssignmentId(String assignmentId);

    // Additional methods for improved functionality
    int countByAssignmentId(String assignmentId);

    Page<StudentAssignment> findByStudentIdOrderBySubmissionDateDesc(String studentId, Pageable pageable);

    @Query("SELECT sa FROM StudentAssignment sa WHERE sa.assignment.id = :assignmentId AND sa.grade IS NOT NULL")
    List<StudentAssignment> findGradedSubmissionsByAssignmentId(@Param("assignmentId") String assignmentId);

    @Query("SELECT COUNT(sa) FROM StudentAssignment sa WHERE sa.assignment.id = :assignmentId AND sa.grade IS NOT NULL")
    int countGradedSubmissionsByAssignmentId(@Param("assignmentId") String assignmentId);
}
