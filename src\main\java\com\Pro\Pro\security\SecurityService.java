package com.Pro.Pro.security;

import com.Pro.Pro.exception.ResourceNotFoundException;
import com.Pro.Pro.exception.ServiceException;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class SecurityService {
    private final UserRepository userRepository;
    private final LessonRepository lessonRepository;
    private final CourseRepository courseRepository;
    private final StudentLessonRepository studentLessonRepository;
    private final ExamRepository examRepository;
//
//    public boolean isAssistantForInstructor(Authentication authentication, String instructorId) {
//        try {
//            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
//
//            if (userPrincipal.getAuthorities().stream()
//                    .anyMatch(a -> a.getAuthority().equals(Role.ASSISTANT.name()))) {
//                User user = userRepository.findById(userPrincipal.getId())
//                        .orElseThrow(() -> new ResourceNotFoundException("User not found"));
//                return user.getInstructor() != null &&
//                        user.getInstructor().getId().equals(instructorId);
//            }
//            return false;
//        } catch (Exception e) {
//            log.error("Error checking assistant-instructor relationship", e);
//            throw new ServiceException("Failed to verify assistant role", HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }
    public boolean isAdmin(Authentication authentication) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
        return userPrincipal.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals(Role.ADMIN.name()));
    }

        public boolean isCourseInstructor(String userId, String courseId) {
            return courseRepository.existsByIdAndInstructorId(courseId, userId);
        }

        public boolean isAssistantForCourse(String userId, String courseId) {
            User user = getUser(userId);
            if (user.getRole() != Role.ASSISTANT || user.getInstructor() == null) {
                return false;
            }
            return courseRepository.existsByIdAndInstructorId(courseId, user.getInstructor().getId());
        }

    public boolean isLessonInstructorOrAssistant(String userId, String lessonId) {
        try {
            User user = getUser(userId);
            Lesson lesson = getLesson(lessonId);

            if (lesson == null || lesson.getCourse() == null) {
                return false;
            }

            // Check if user is the instructor
            if (user.getId().equals(lesson.getCourse().getInstructor().getId())) {
                return true;
            }

            // Check if user is an assistant for this instructor
            return user.getRole() == Role.ASSISTANT &&
                    user.getInstructor() != null &&
                    user.getInstructor().getId().equals(lesson.getCourse().getInstructor().getId());
        } catch (ResourceNotFoundException e) {
            log.warn("Lesson or user not found", e);
            return false;
        } catch (Exception e) {
            log.error("Error verifying lesson instructor/assistant relationship", e);
            throw new ServiceException("Failed to verify lesson access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    public boolean hasLessonAccess(String userId, String lessonId) {
        try {
            // Check if student has valid access to the lesson
            return studentLessonRepository.existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
                    userId,
                    lessonId,
                    new java.util.Date()
            );
        } catch (Exception e) {
            log.error("Error checking lesson access for user {}", userId, e);
            throw new ServiceException("Failed to check lesson access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isCourseInstructorOrAssistant(Authentication authentication, String courseId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            User user = getUser(userPrincipal.getId());

            if (courseRepository.existsByIdAndInstructorId(courseId, user.getId())) {
                return true;
            }

            return user.getRole() == Role.ASSISTANT &&
                    user.getInstructor() != null &&
                    courseRepository.existsByIdAndInstructorId(courseId, user.getInstructor().getId());
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error verifying course instructor/assistant", e);
            throw new ServiceException("Failed to verify course access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isLessonInstructorOrAssistant(Authentication authentication, String lessonId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            return isLessonInstructorOrAssistant(userPrincipal.getId(), lessonId);
        } catch (Exception e) {
            log.error("Error verifying lesson instructor/assistant", e);
            throw new ServiceException("Failed to verify lesson access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    public boolean isLessonInstructorOrAssistant(String userId, String lessonId) {
//        try {
//            User user = getUser(userId);
//            Lesson lesson = getLesson(lessonId);
//
//            // Check if user is the instructor
//            if (user.getId().equals(lesson.getCourse().getInstructor().getId())) {
//                return true;
//            }
//
//            // Check if user is an assistant for this instructor
//            return user.getRole() == Role.ASSISTANT &&
//                    user.getInstructor() != null &&
//                    user.getInstructor().getId().equals(lesson.getCourse().getInstructor().getId());
//        } catch (ResourceNotFoundException e) {
//            throw e;
//        } catch (Exception e) {
//            log.error("Error verifying lesson instructor/assistant relationship", e);
//            throw new ServiceException("Failed to verify lesson access", HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

public boolean hasExamAccess(String userId, String examId) {
    try {
        Exam exam = examRepository.findById(examId)
                .orElseThrow(() -> new ResourceNotFoundException("Exam not found"));
        return hasLessonAccess(userId, exam.getLesson().getId());
    } catch (ResourceNotFoundException e) {
        return false;
    } catch (Exception e) {
        log.error("Error checking exam access", e);
        throw new ServiceException("Failed to verify exam access", HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

    public boolean isExamInstructorOrAssistant(Authentication authentication, String examId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            Exam exam = examRepository.findById(examId)
                    .orElseThrow(() -> new ResourceNotFoundException("Exam not found"));
            return isLessonInstructorOrAssistant(userPrincipal.getId(), exam.getLesson().getId());
        } catch (ResourceNotFoundException e) {
            return false;
        } catch (Exception e) {
            log.error("Error verifying exam instructor/assistant", e);
            throw new ServiceException("Failed to verify exam access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isAssignmentInstructorOrAssistant(String userId, String assignmentId) {
        try {
            // Implementation would be similar to lesson check but for assignments
            // You would need an assignmentRepository to look up the assignment's lesson/course
            // Then verify the user is instructor/assistant for that course
            return true; // Placeholder - implement based on your assignment structure
        } catch (Exception e) {
            log.error("Error verifying assignment access", e);
            throw new ServiceException("Failed to verify assignment access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isStudentOwner(String studentId, String resourceId) {
        try {
            // Generic method to check if student owns a resource
            // Could be used for various student-owned resources
            return true; // Placeholder - implement based on your requirements
        } catch (Exception e) {
            log.error("Error verifying student ownership", e);
            throw new ServiceException("Failed to verify ownership", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    public boolean isAssistantForInstructor(Authentication authentication, String instructorId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            User user = userRepository.findById(userPrincipal.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));

            // Check if user is an assistant and is assigned to the specified instructor
            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && user.getInstructor().getId().equals(instructorId);
        } catch (Exception e) {
            log.error("Error checking assistant-instructor relationship", e);
            throw new ServiceException("Failed to verify assistant role", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // Helper methods
    private User getUser(String userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }

    private Lesson getLesson(String lessonId) {
        return lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("Lesson not found"));
    }
}