package com.Pro.Pro.security;

import com.Pro.Pro.exception.ResourceNotFoundException;
import com.Pro.Pro.exception.ServiceException;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class SecurityService {

    private static final Logger log = LoggerFactory.getLogger(SecurityService.class);
    private final UserRepository userRepository;
    private final LessonRepository lessonRepository;
    private final CourseRepository courseRepository;
    private final StudentLessonRepository studentLessonRepository;
    private final ExamRepository examRepository;
    private final AssignmentRepository assignmentRepository;

    // =============== Role Checks ===============
    public boolean isAdmin(Authentication authentication) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
        return userPrincipal.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals(Role.ADMIN.name()));
    }

    public boolean isAssistantForInstructor(Authentication authentication, String instructorId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            User user = getUser(userPrincipal.getId());

            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && user.getInstructor().getId().equals(instructorId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من علاقة المساعد بالمدرس", e);
            throw new ServiceException("فشل في التحقق من دور المساعد", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // =============== Course Access Checks ===============
    public boolean isCourseInstructorOrAssistant(Authentication authentication, String courseId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            User user = getUser(userPrincipal.getId());

            if (isCourseInstructor(user.getId(), courseId)) {
                return true;
            }

            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && isCourseInstructor(user.getInstructor().getId(), courseId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الكورس", e);
            throw new ServiceException("فشل في التحقق من الوصول للكورس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isCourseInstructor(String userId, String courseId) {
        return courseRepository.existsByIdAndInstructorId(courseId, userId);
    }

    // =============== Lesson Access Checks ===============
    public boolean isLessonInstructorOrAssistant(Authentication authentication, String lessonId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            return isLessonInstructorOrAssistant(userPrincipal.getId(), lessonId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الدرس", e);
            throw new ServiceException("فشل في التحقق من الوصول للدرس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isInstructorOrAssistant(Authentication authentication, String instructorId) {
        try {
            if (authentication == null || authentication.getPrincipal() == null) {
                return false;
            }

            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            User user = getUser(userPrincipal.getId());

            // Admin has full access
            if (user.getRole() == Role.ADMIN) {
                return true;
            }

            // Check if user is the instructor
            if (user.getId().equals(instructorId) && user.getRole() == Role.INSTRUCTOR) {
                return true;
            }

            // Check if user is assistant for this instructor
            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && user.getInstructor().getId().equals(instructorId);
        } catch (Exception e) {
            log.error("Error checking instructor/assistant relationship", e);
            return false;
        }
    }

    public boolean isLessonInstructorOrAssistant(String userId, String lessonId) {
        try {
            User user = getUser(userId);
            Lesson lesson = getLesson(lessonId);

            if (user.getId().equals(lesson.getCourse().getInstructor().getId())) {
                return true;
            }

            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && user.getInstructor().getId().equals(lesson.getCourse().getInstructor().getId());
        } catch (ResourceNotFoundException e) {
            log.warn("الدرس أو المستخدم غير موجود", e);
            return false;
        } catch (Exception e) {
            log.error("خطأ في التحقق من علاقة مدرس/مساعد الدرس", e);
            throw new ServiceException("فشل في التحقق من الوصول للدرس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean hasLessonAccess(String userId, String lessonId) {
        try {
            return studentLessonRepository.existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
                    userId,
                    lessonId,
                    new java.util.Date());
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول للدرس للمستخدم {}", userId, e);
            throw new ServiceException("فشل في التحقق من الوصول للدرس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Check if user has access to assignment (either has lesson access or is instructor/assistant)
     */
    public boolean hasAssignmentAccess(String userId, String assignmentId) {
        try {
            // Get assignment and its lesson
            Assignment assignment = assignmentRepository.findById(assignmentId).orElse(null);
            if (assignment == null) {
                return false;
            }

            String lessonId = assignment.getLesson().getId();

            // Check if user has lesson access or is instructor/assistant
            return hasLessonAccess(userId, lessonId) ||
                   isLessonInstructorOrAssistant(userId, lessonId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول للواجب للمستخدم {}", userId, e);
            return false;
        }
    }

    /**
     * Check if authenticated user has access to assignment
     */
    public boolean hasAssignmentAccess(Authentication authentication, String assignmentId) {
        try {
            if (authentication == null || authentication.getName() == null) {
                return false;
            }
            String email = authentication.getName(); // UserDetailsImpl.getUsername() returns email
            User user = userRepository.findByEmail(email).orElse(null);
            if (user == null) {
                return false;
            }
            return hasAssignmentAccess(user.getId(), assignmentId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول للواجب للمستخدم المصادق عليه", e);
            return false;
        }
    }

    /**
     * Check if user is instructor or assistant for assignment
     */
    public boolean isAssignmentInstructorOrAssistant(String userId, String assignmentId) {
        try {
            // Get assignment and its lesson
            Assignment assignment = assignmentRepository.findById(assignmentId).orElse(null);
            if (assignment == null) {
                return false;
            }

            String lessonId = assignment.getLesson().getId();
            return isLessonInstructorOrAssistant(userId, lessonId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الواجب للمستخدم {}", userId, e);
            return false;
        }
    }

    /**
     * Check if authenticated user is instructor or assistant for assignment
     */
    public boolean isAssignmentInstructorOrAssistant(Authentication authentication, String assignmentId) {
        try {
            if (authentication == null || authentication.getName() == null) {
                return false;
            }
            String email = authentication.getName(); // UserDetailsImpl.getUsername() returns email
            User user = userRepository.findByEmail(email).orElse(null);
            if (user == null) {
                return false;
            }
            return isAssignmentInstructorOrAssistant(user.getId(), assignmentId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الواجب للمستخدم المصادق عليه", e);
            return false;
        }
    }

    /**
     * Check if user has access to exam
     */
    public boolean hasExamAccess(String userId, String examId) {
        try {
            // Get exam and its lesson
            Exam exam = examRepository.findById(examId).orElse(null);
            if (exam == null) {
                return false;
            }

            String lessonId = exam.getLesson().getId();

            // Check if user has lesson access or is instructor/assistant
            return hasLessonAccess(userId, lessonId) ||
                   isLessonInstructorOrAssistant(userId, lessonId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول للامتحان للمستخدم {}", userId, e);
            return false;
        }
    }

    /**
     * Check if authenticated user has access to exam
     */
    public boolean hasExamAccess(Authentication authentication, String examId) {
        try {
            if (authentication == null || authentication.getName() == null) {
                return false;
            }
            String email = authentication.getName(); // UserDetailsImpl.getUsername() returns email
            User user = userRepository.findByEmail(email).orElse(null);
            if (user == null) {
                return false;
            }
            return hasExamAccess(user.getId(), examId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول للامتحان للمستخدم المصادق عليه", e);
            return false;
        }
    }

    /**
     * Check if user is instructor or assistant for exam
     */
    public boolean isExamInstructorOrAssistant(String userId, String examId) {
        try {
            // Get exam and its lesson
            Exam exam = examRepository.findById(examId).orElse(null);
            if (exam == null) {
                return false;
            }

            String lessonId = exam.getLesson().getId();
            return isLessonInstructorOrAssistant(userId, lessonId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الامتحان للمستخدم {}", userId, e);
            return false;
        }
    }

    /**
     * Check if authenticated user is instructor or assistant for exam
     */
    public boolean isExamInstructorOrAssistant(Authentication authentication, String examId) {
        try {
            if (authentication == null || authentication.getName() == null) {
                return false;
            }
            String email = authentication.getName(); // UserDetailsImpl.getUsername() returns email
            User user = userRepository.findByEmail(email).orElse(null);
            if (user == null) {
                return false;
            }
            return isExamInstructorOrAssistant(user.getId(), examId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الامتحان للمستخدم المصادق عليه", e);
            return false;
        }
    }

    public boolean isStudentOwner(String studentId, String lessonId) {
        try {
            // Check if student has access to this lesson
            return studentLessonRepository.existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
                    studentId,
                    lessonId,
                    new Date()
            );
        } catch (Exception e) {
            log.error("Error checking student lesson ownership", e);
            return false;
        }
    }

    /**
     * Enhanced lesson access check that allows:
     * - ADMIN: Full access to any lesson
     * - INSTRUCTOR/ASSISTANT: Access to their own lessons only
     * - STUDENT: Access to purchased lessons only (existing behavior)
     */
    public boolean hasEnhancedLessonAccess(Authentication authentication, String lessonId) {
        try {
            if (authentication == null || authentication.getPrincipal() == null) {
                return false;
            }

            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            String userId = userPrincipal.getId();

            // Get user to check role
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return false;
            }

            // ADMIN has access to any lesson
            if (user.getRole() == Role.ADMIN) {
                log.debug("Admin {} granted access to lesson {}", userId, lessonId);
                return true;
            }

            // INSTRUCTOR/ASSISTANT can access their own lessons
            if (user.getRole() == Role.INSTRUCTOR || user.getRole() == Role.ASSISTANT) {
                boolean hasInstructorAccess = isLessonInstructorOrAssistant(userId, lessonId);
                if (hasInstructorAccess) {
                    log.debug("Instructor/Assistant {} granted access to their lesson {}", userId, lessonId);
                }
                return hasInstructorAccess;
            }

            // STUDENT can access purchased lessons (existing behavior)
            if (user.getRole() == Role.STUDENT) {
                boolean hasStudentAccess = isStudentOwner(userId, lessonId);
                if (hasStudentAccess) {
                    log.debug("Student {} granted access to purchased lesson {}", userId, lessonId);
                }
                return hasStudentAccess;
            }

            return false;
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول المحسن للدرس للمستخدم", e);
            return false;
        }
    }




    // =============== Helper Methods ===============
    private User getUser(String userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }

    private Lesson getLesson(String lessonId) {
        return lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("Lesson not found"));
    }
}