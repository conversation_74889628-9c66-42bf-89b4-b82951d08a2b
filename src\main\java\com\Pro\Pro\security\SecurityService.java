package com.Pro.Pro.security;

import com.Pro.Pro.exception.ResourceNotFoundException;
import com.Pro.Pro.exception.ServiceException;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class SecurityService {
    private final UserRepository userRepository;
    private final LessonRepository lessonRepository;
    private final CourseRepository courseRepository;
    private final StudentLessonRepository studentLessonRepository;
    private final ExamRepository examRepository;
    private final AssignmentRepository assignmentRepository;

    // =============== Role Checks ===============
    public boolean isAdmin(Authentication authentication) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
        return userPrincipal.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals(Role.ADMIN.name()));
    }

    public boolean isAssistantForInstructor(Authentication authentication, String instructorId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            User user = getUser(userPrincipal.getId());

            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && user.getInstructor().getId().equals(instructorId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من علاقة المساعد بالمدرس", e);
            throw new ServiceException("فشل في التحقق من دور المساعد", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // =============== Course Access Checks ===============
    public boolean isCourseInstructorOrAssistant(Authentication authentication, String courseId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            User user = getUser(userPrincipal.getId());

            if (isCourseInstructor(user.getId(), courseId)) {
                return true;
            }

            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && isCourseInstructor(user.getInstructor().getId(), courseId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الكورس", e);
            throw new ServiceException("فشل في التحقق من الوصول للكورس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isCourseInstructor(String userId, String courseId) {
        return courseRepository.existsByIdAndInstructorId(courseId, userId);
    }

    // =============== Lesson Access Checks ===============
    public boolean isLessonInstructorOrAssistant(Authentication authentication, String lessonId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            return isLessonInstructorOrAssistant(userPrincipal.getId(), lessonId);
        } catch (Exception e) {
            log.error("خطأ في التحقق من مدرس/مساعد الدرس", e);
            throw new ServiceException("فشل في التحقق من الوصول للدرس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isLessonInstructorOrAssistant(String userId, String lessonId) {
        try {
            User user = getUser(userId);
            Lesson lesson = getLesson(lessonId);

            if (user.getId().equals(lesson.getCourse().getInstructor().getId())) {
                return true;
            }

            return user.getRole() == Role.ASSISTANT
                    && user.getInstructor() != null
                    && user.getInstructor().getId().equals(lesson.getCourse().getInstructor().getId());
        } catch (ResourceNotFoundException e) {
            log.warn("الدرس أو المستخدم غير موجود", e);
            return false;
        } catch (Exception e) {
            log.error("خطأ في التحقق من علاقة مدرس/مساعد الدرس", e);
            throw new ServiceException("فشل في التحقق من الوصول للدرس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean hasLessonAccess(String userId, String lessonId) {
        try {
            return studentLessonRepository.existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
                    userId,
                    lessonId,
                    new java.util.Date());
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول للدرس للمستخدم {}", userId, e);
            throw new ServiceException("فشل في التحقق من الوصول للدرس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isStudentOwner(String studentId, String lessonId) {
        try {
            // Check if student has access to this lesson
            return studentLessonRepository.existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
                    studentId,
                    lessonId,
                    new Date()
            );
        } catch (Exception e) {
            log.error("Error checking student lesson ownership", e);
            return false;
        }
    }
    // =============== Exam Access Checks ===============
    public boolean hasExamAccess(String userId, String examId) {
        try {
            Exam exam = examRepository.findById(examId)
                    .orElseThrow(() -> new ResourceNotFoundException("Exam not found"));
            return hasLessonAccess(userId, exam.getLesson().getId());
        } catch (ResourceNotFoundException e) {
            return false;
        } catch (Exception e) {
            log.error("Error checking exam access", e);
            throw new ServiceException("Failed to verify exam access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public boolean isExamInstructorOrAssistant(Authentication authentication, String examId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            Exam exam = examRepository.findById(examId)
                    .orElseThrow(() -> new ResourceNotFoundException("Exam not found"));
            return isLessonInstructorOrAssistant(userPrincipal.getId(), exam.getLesson().getId());
        } catch (ResourceNotFoundException e) {
            return false;
        } catch (Exception e) {
            log.error("Error verifying exam instructor/assistant", e);
            throw new ServiceException("Failed to verify exam access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // =============== Assignment Access Checks ===============
    public boolean isAssignmentInstructorOrAssistant(Authentication authentication, String assignmentId) {
        try {
            UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
            Assignment assignment = assignmentRepository.findById(assignmentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Assignment not found"));
            return isLessonInstructorOrAssistant(userPrincipal.getId(), assignment.getLesson().getId());
        } catch (ResourceNotFoundException e) {
            return false;
        } catch (Exception e) {
            log.error("Error verifying assignment instructor/assistant", e);
            throw new ServiceException("Failed to verify assignment access", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // =============== Helper Methods ===============
    private User getUser(String userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }

    private Lesson getLesson(String lessonId) {
        return lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("Lesson not found"));
    }
}