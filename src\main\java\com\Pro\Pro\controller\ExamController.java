package com.Pro.Pro.controller;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.security.CurrentUser;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.ExamService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/exams")
@RequiredArgsConstructor
public class ExamController {

    private final ExamService examService;

    @PostMapping("/lessons/{lessonId}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    public ResponseEntity<ExamResponse> createExam(
            @PathVariable String lessonId,
            @Valid @RequestBody ExamRequest request) {
        try {
            return ResponseEntity.ok(examService.createExam(request, lessonId));
        } catch (BadRequestException e) {
            return ResponseEntity.badRequest().body(
                    ExamResponse.builder().errorMessage(e.getMessage()).build());
        } catch (Exception e) {
            log.error("Error creating exam", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{examId}")
    @PreAuthorize("@securityService.hasLessonAccess(authentication.principal.id, #examId) or " +
            "@securityService.isLessonInstructorOrAssistant(authentication, #examId)")
    public ResponseEntity<ExamResponse> getExam(@PathVariable String examId) {
        try {
            return ResponseEntity.ok(examService.getExam(examId));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error getting exam", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{examId}/results")  // NEW endpoint for exam results
    @PreAuthorize("hasRole('INSTRUCTOR') or hasRole('ADMIN')")
    public ResponseEntity<ExamResultResponse> getExamResults(@PathVariable String examId) {
        try {
            return ResponseEntity.ok(examService.getExamResults(examId));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error getting exam results", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/{examId}/submit")
    @PreAuthorize("hasRole('STUDENT')")
    public ResponseEntity<ExamResponse> submitExam(
            @PathVariable String examId,
            @RequestBody Map<String, String> answers,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            return ResponseEntity.ok(examService.submitExam(examId, answers, currentUser.getId()));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (BadRequestException | LessonAccessException e) {
            return ResponseEntity.badRequest().body(
                    ExamResponse.builder().errorMessage(e.getMessage()).build());
        } catch (Exception e) {
            log.error("Error submitting exam", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @DeleteMapping("/{examId}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @examRepository.findById(#examId).orElseThrow().lesson.id)")
    public ResponseEntity<Void> deleteExam(@PathVariable String examId) {
        try {
            examService.deleteExam(examId);
            return ResponseEntity.noContent().build();
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error deleting exam", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @examRepository.findById(#request.examId).orElseThrow().lesson.id)")
    public ResponseEntity<ExamResponse> updateExam(@Valid @RequestBody ExamUpdateRequest request) {
        try {
            return ResponseEntity.ok(examService.updateExam(request));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error updating exam", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}