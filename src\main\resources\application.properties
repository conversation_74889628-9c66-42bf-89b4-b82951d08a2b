spring.application.name=Pro
server.port=8080
spring.jpa.hibernate.ddl-auto=update
# Database Configuration
spring.jpa.open-in-view=false
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=************************************
spring.datasource.username=postgres
spring.datasource.password=MOHAMEDALI20042005
# JPA Configuration
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
# Remove Gemini properties and add OpenRouter properties
openrouter.api.key=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
openrouter.api.url=https://openrouter.ai/api/v1/chat/completions
openrouter.api.referer=http://localhost:8080
# Change to your domain
openrouter.api.title=Pro
# Change to your app name
openrouter.api.model=openai/gpt-4o
# Default model#spring.main.web-application-type=reactive
spring.main.allow-bean-definition-overriding=true
# Video streaming configuration
video.storage.location=/path/to/video/storage
video.allowed-extensions=.mp4,.mov,.avi
# application.properties
Pro.jwtSecret=yptWwyqsIlQHx19x29ds9ViMKFldWXdU/espBZ+kf2hpplu5oQF/mypXrTvARduEGBe7tVqeFU1xLngRipt4+w==
Pro.jwtExpirationMs=86400000
# Add these if they don't exist
spring.main.web-application-type=servlet
#spring.jpa.properties.hibernate.cache.use_second_level_cache=true
#spring.jpa.properties.hibernate.cache.region.factory_class=org.hibernate.cache.jcache.JCacheRegionFactory
Pro.refreshExpirationMs=2592000000
server.tomcat.uri-encoding=UTF-8
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true
FRONTEND_URL=http://localhost:3000

