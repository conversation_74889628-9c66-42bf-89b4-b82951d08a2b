package com.Pro.Pro.controller;

import com.Pro.Pro.dto.request.LessonAccessRequest;
import com.Pro.Pro.dto.response.PaymentResponse;
import com.Pro.Pro.security.CurrentUser;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/payments")
@RequiredArgsConstructor
public class PaymentController {

    private final PaymentService paymentService;

    @Operation(summary = "Grant lesson access")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Access granted"),
            @ApiResponse(responseCode = "400", description = "Invalid request"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/access-lesson")
    public ResponseEntity<?> grantLessonAccess(
            @RequestBody @Valid LessonAccessRequest request,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            PaymentResponse response = paymentService.grantLessonAccess(
                    request,
                    currentUser.getId()
            );
            return ResponseEntity.ok(response);
        } catch (BadRequestException e) {
            log.error("Invalid request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Error granting lesson access", e);
            return ResponseEntity.internalServerError().body(Map.of("error", "Failed to grant lesson access"));
        }
    }

    @GetMapping("/check-access/{lessonId}")
    public ResponseEntity<?> checkLessonAccess(
            @PathVariable String lessonId,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            boolean hasAccess = paymentService.hasValidAccess(currentUser.getId(), lessonId);

            if (!hasAccess) {
                LocalDate expiryDate = paymentService.getAccessExpiryDate(currentUser.getId(), lessonId);
                return ResponseEntity.ok(Map.of(
                        "hasAccess", false,
                        "message", "Access expired on " + expiryDate,
                        "canRepurchase", true
                ));
            }

            return ResponseEntity.ok(Map.of(
                    "hasAccess", true,
                    "expiryDate", paymentService.getAccessExpiryDate(currentUser.getId(), lessonId)
            ));
        } catch (Exception e) {
            log.error("Error checking lesson access", e);
            throw e;
        }
    }

    @GetMapping("/access-expiry/{lessonId}")
    public ResponseEntity<LocalDate> getAccessExpiryDate(
            @PathVariable String lessonId,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            LocalDate expiryDate = paymentService.getAccessExpiryDate(
                    currentUser.getId(),
                    lessonId
            );
            return ResponseEntity.ok(expiryDate);
        } catch (Exception e) {
            log.error("Error getting access expiry date", e);
            throw e;
        }
    }

    @GetMapping("/payment-status/{lessonId}")
    public ResponseEntity<String> getPaymentStatus(
            @PathVariable String lessonId,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            String status = paymentService.getPaymentStatus(currentUser.getId(), lessonId);
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("Error getting payment status", e);
            throw e;
        }
    }

}
