package com.Pro.Pro.repository;

import com.Pro.Pro.model.Course;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CourseRepository extends JpaRepository<Course, String> {
    List<Course> findByInstructorId(String instructorId);
    boolean existsByIdAndInstructorId(String courseId, String instructorId);

    @Query("SELECT DISTINCT c FROM Course c LEFT JOIN FETCH c.lessons WHERE c.instructor.id = :instructorId")
    List<Course> findByInstructorIdWithLessons(@Param("instructorId") String instructorId);

    @Query(value = """
        SELECT c.id, c.name, c.description, c.photo_url, 
               COUNT(l.id) as lesson_count 
        FROM courses c 
        LEFT JOIN lessons l ON c.id = l.course_id 
        WHERE c.instructor_id = :instructorId 
        GROUP BY c.id
        """, nativeQuery = true)
    List<Object[]> findCourseSummariesByInstructorId(@Param("instructorId") String instructorId);

    @Query("SELECT c FROM Course c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
            "OR LOWER(c.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Course> searchCourses(@Param("searchTerm") String searchTerm, Pageable pageable);

    @Query("SELECT c FROM Course c WHERE c.instructor.id = :instructorId ORDER BY c.createdAt DESC")
    Page<Course> findByInstructorIdPaginated(@Param("instructorId") String instructorId, Pageable pageable);
}