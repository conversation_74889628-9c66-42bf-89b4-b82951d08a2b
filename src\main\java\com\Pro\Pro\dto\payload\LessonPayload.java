package com.Pro.Pro.dto.payload;

import com.Pro.Pro.dto.response.AssignmentResponse;
import com.Pro.Pro.dto.response.ExamResponse;
import com.Pro.Pro.dto.response.LessonResponse;
import com.Pro.Pro.model.LessonProgressStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LessonPayload {
    private LessonResponse lesson;
    private ExamResponse exam;
    private AssignmentResponse assignment;
    private boolean hasAccess;
    private String videoUrl;
    private String accessError;
    private LessonProgressStatus progressStatus; // Add current progress

    // Add content availability flags
    private boolean canAccessVideo;
    private boolean canAccessExam;
    private boolean canAccessAssignment;
}