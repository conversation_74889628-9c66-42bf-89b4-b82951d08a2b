package com.Pro.Pro.controller;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.AccessCode;
import com.Pro.Pro.model.User;
import com.Pro.Pro.security.CurrentUser;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.AdminService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasRole('ADMIN')")
@RequiredArgsConstructor
public class AdminController {
    private final AdminService adminService;

    @PostMapping("/instructors")
    public ResponseEntity<?> createInstructor(@Valid @RequestBody CreateInstructorRequest request) {
        try {
            return ResponseEntity.ok(adminService.createInstructor(request));
        } catch (CustomException e) {
            log.error("Failed to create instructor: {}", e.getMessage(), e);
            return ResponseEntity.status(e.getStatus()).body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error creating instructor", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred while creating instructor");
        }
    }

    @PostMapping("/assistants/{instructorId}")
    public ResponseEntity<?> createAssistant(
            @PathVariable String instructorId,
            @Valid @RequestBody CreateAssistantRequest request) {
        try {
            return ResponseEntity.ok(adminService.createAssistant(request, instructorId));
        } catch (ResourceNotFoundException e) {
            log.error("Instructor not found: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (CustomException e) {
            log.error("Failed to create assistant: {}", e.getMessage(), e);
            return ResponseEntity.status(e.getStatus()).body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error creating assistant", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred while creating assistant");
        }
    }

    @PostMapping("/courses/{instructorId}")
    public ResponseEntity<?> createCourse(
            @PathVariable String instructorId,
            @Valid @RequestBody CourseRequest request) {
        try {
            return ResponseEntity.ok(adminService.createCourse(request, instructorId));
        } catch (ResourceNotFoundException e) {
            log.error("Instructor not found: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error creating course", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred while creating course");
        }
    }

    @PostMapping("/lessons/{courseId}")
    public ResponseEntity<?> createLesson(
            @PathVariable String courseId,
            @Valid @RequestBody LessonRequest request) {
        try {
            return ResponseEntity.ok(adminService.createLesson(request, courseId));
        } catch (ResourceNotFoundException e) {
            log.error("Course not found: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error creating lesson", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred while creating lesson");
        }
    }

    @PostMapping("/lessons/{lessonId}/generate-codes")
    public ResponseEntity<?> generateAccessCodes(
            @PathVariable String lessonId,
            @RequestParam int count,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            return ResponseEntity.ok(adminService.generateAccessCodes(lessonId, count, currentUser.getId()));
        } catch (ResourceNotFoundException e) {
            log.error("Resource not found: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error generating access codes", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred while generating access codes");
        }
    }

    @GetMapping("/students")
    public ResponseEntity<?> getAllStudents() {
        try {
            return ResponseEntity.ok(adminService.getAllStudents());
        } catch (Exception e) {
            log.error("Failed to retrieve students", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to retrieve students");
        }
    }

    @DeleteMapping("/users/{userId}")
    public ResponseEntity<?> deleteUser(@PathVariable String userId) {
        try {
            adminService.deleteUser(userId);
            return ResponseEntity.noContent().build();
        } catch (ResourceNotFoundException e) {
            log.error("User not found: {}", userId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to delete user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to delete user");
        }
    }

    @DeleteMapping("/courses/{courseId}")
    public ResponseEntity<?> deleteCourse(@PathVariable String courseId) {
        try {
            adminService.deleteCourse(courseId);
            return ResponseEntity.noContent().build();
        } catch (ResourceNotFoundException e) {
            log.error("Course not found: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to delete course: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to delete course");
        }
    }

    @DeleteMapping("/lessons/{lessonId}")
    public ResponseEntity<?> deleteLesson(@PathVariable String lessonId) {
        try {
            adminService.deleteLesson(lessonId);
            return ResponseEntity.noContent().build();
        } catch (ResourceNotFoundException e) {
            log.error("Lesson not found: {}", lessonId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to delete lesson: {}", lessonId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to delete lesson");
        }
    }

    @PutMapping("/courses/{courseId}")
    public ResponseEntity<?> updateCourse(
            @PathVariable String courseId,
            @Valid @RequestBody CourseRequest request) {
        try {
            return ResponseEntity.ok(adminService.updateCourse(courseId, request));
        } catch (ResourceNotFoundException e) {
            log.error("Course not found: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to update course: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to update course");
        }
    }

    @PutMapping("/lessons/{lessonId}")
    public ResponseEntity<?> updateLesson(
            @PathVariable String lessonId,
            @Valid @RequestBody LessonRequest request) {
        try {
            return ResponseEntity.ok(adminService.updateLesson(lessonId, request));
        } catch (ResourceNotFoundException e) {
            log.error("Lesson not found: {}", lessonId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to update lesson: {}", lessonId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to update lesson");
        }
    }

    @PutMapping("/instructors/id/profile")
    public ResponseEntity<?> updateInstructorProfile(
            @PathVariable String instructorId,
            @Valid @RequestBody UpdateProfileRequest request) {
        try {
            return ResponseEntity.ok(adminService.updateInstructorProfile(instructorId, request));
        } catch (ResourceNotFoundException e) {
            log.error("Instructor not found: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to update instructor profile: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to update instructor profile");
        }
    }

    @PostMapping("/search")
    public ResponseEntity<List<User>> searchStudentsByName(@Valid @RequestBody Map<String, String> body) {
        String usernamePart = body.get("usernamePart");
        List<User> students = adminService.searchStudentsByUsername(usernamePart);
        if (students.isEmpty()) {
            return ResponseEntity.notFound().build();
        } else {
            return ResponseEntity.ok(students);
        }
    }
}