package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.CourseRequest;
import com.Pro.Pro.dto.request.LessonRequest;
import com.Pro.Pro.dto.request.UpdateProfileRequest;
import com.Pro.Pro.dto.response.CourseResponse;
import com.Pro.Pro.dto.response.LessonResponse;
import com.Pro.Pro.dto.response.UserResponse;
import com.Pro.Pro.model.AccessCode;

import java.util.List;
import java.util.UUID;

public interface InstructorService {
    CourseResponse createCourse(CourseRequest request, String instructorId);
    LessonResponse createLesson(LessonRequest request, String courseId);
    List<AccessCode> generateAccessCodes(String lessonId, int count , String creatorId);
    List<CourseResponse> getInstructorCourses(String instructorId);
    List<LessonResponse> getCourseLessons(String courseId);
    Long getStudentCount(String instructorId);
    CourseResponse updateOwnCourse(String courseId, CourseRequest request, String instructorId);
    LessonResponse updateOwnLesson(String lessonId, LessonRequest request, String instructorId);
    UserResponse updateOwnProfile(String instructorId, UpdateProfileRequest request);
}