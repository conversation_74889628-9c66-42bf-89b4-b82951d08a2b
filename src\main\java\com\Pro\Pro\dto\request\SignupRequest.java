package com.Pro.Pro.dto.request;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SignupRequest {
    @NotBlank
    @Email
    private String email;

    @NotBlank @Size(min = 8)
    private String password;

    @NotBlank
    private String username;

    @NotBlank
    private String fullname;

    @NotBlank private String phoneNumber;

    @NotBlank private String parentPhoneNumber;

    @NotNull
    private Date dateOfBirth;

    @NotBlank private String nationalId;

    @NotBlank private String government;

}