package com.Pro.Pro.repository;

import com.Pro.Pro.model.LessonProgressStatus;
import com.Pro.Pro.model.PaymentMethod;
import com.Pro.Pro.model.StudentLesson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface StudentLessonRepository extends JpaRepository<StudentLesson, String> {
    List<StudentLesson> findByStudentId(String studentId);
    boolean existsByStudentIdAndLessonId(String studentId, String lessonId);
    Long countByLessonCourseInstructorId(String instructorId);

    boolean existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
            String studentId,
            String lessonId,
            Date currentDate
    );


    @Modifying
    @Query("DELETE FROM StudentLesson sl WHERE sl.student.id = :studentId AND sl.lesson.id = :lessonId")
    void deleteByStudentIdAndLessonId(@Param("studentId") String studentId, @Param("lessonId") String lessonId);

    Optional<StudentLesson> findByStudentIdAndLessonId(String studentId, String lessonId);

    @Query(value = """
        SELECT sl.progress_status, COUNT(*) 
        FROM student_lessons sl 
        WHERE sl.lesson_id = :lessonId 
        GROUP BY sl.progress_status
        """, nativeQuery = true)
    List<Object[]> countStudentsByProgressStatus(@Param("lessonId") String lessonId);

    @Query(value = """
        UPDATE student_lessons 
        SET access_expiry_date = access_expiry_date + INTERVAL '7 days' 
        WHERE student_id = :studentId 
        AND lesson_id = :lessonId 
        AND access_expiry_date > NOW()
        """, nativeQuery = true)
    @Modifying
    void extendAccess(@Param("studentId") String studentId, @Param("lessonId") String lessonId);

    @Query("SELECT COUNT(sl) FROM StudentLesson sl WHERE sl.lesson.id = :lessonId")
    long countByLessonId(@Param("lessonId") String lessonId);

    // =============== PostgreSQL Optimized Queries ===============

    /**
     * Find student lessons with pagination and sorting
     */
    @Query("SELECT sl FROM StudentLesson sl WHERE sl.student.id = :studentId ORDER BY sl.paymentDate DESC")
    Page<StudentLesson> findByStudentIdOrderByPaymentDateDesc(@Param("studentId") String studentId, Pageable pageable);

    /**
     * Find active lessons for student (not expired)
     */
    @Query("SELECT sl FROM StudentLesson sl WHERE sl.student.id = :studentId " +
           "AND (sl.accessExpiryDate IS NULL OR sl.accessExpiryDate > CURRENT_TIMESTAMP) " +
           "ORDER BY sl.paymentDate DESC")
    List<StudentLesson> findActiveByStudentId(@Param("studentId") String studentId);

    /**
     * Find lessons by progress status with pagination
     */
    @Query("SELECT sl FROM StudentLesson sl WHERE sl.student.id = :studentId " +
           "AND sl.progressStatus = :status ORDER BY sl.paymentDate DESC")
    Page<StudentLesson> findByStudentIdAndProgressStatus(@Param("studentId") String studentId,
                                                        @Param("status") LessonProgressStatus status,
                                                        Pageable pageable);

    /**
     * Batch update progress status for multiple lessons
     */
    @Modifying
    @Query("UPDATE StudentLesson sl SET sl.progressStatus = :newStatus, sl.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE sl.student.id = :studentId AND sl.lesson.id IN :lessonIds")
    int updateProgressStatusBatch(@Param("studentId") String studentId,
                                 @Param("lessonIds") List<String> lessonIds,
                                 @Param("newStatus") LessonProgressStatus newStatus);

    /**
     * Find lessons expiring soon (within specified days)
     */
    @Query(value = """
        SELECT sl.* FROM student_lessons sl
        WHERE sl.student_id = :studentId
        AND sl.access_expiry_date BETWEEN NOW() AND NOW() + INTERVAL ':days days'
        ORDER BY sl.access_expiry_date ASC
        """, nativeQuery = true)
    List<StudentLesson> findLessonsExpiringSoon(@Param("studentId") String studentId, @Param("days") int days);

    /**
     * Get lesson statistics for instructor dashboard
     */
    @Query(value = """
        SELECT
            l.id as lesson_id,
            l.name as lesson_name,
            COUNT(sl.id) as total_students,
            COUNT(CASE WHEN sl.progress_status = 'COMPLETED' THEN 1 END) as completed_students,
            COUNT(CASE WHEN sl.access_expiry_date > NOW() THEN 1 END) as active_students,
            AVG(CASE WHEN sl.progress_status = 'COMPLETED' THEN 100.0 ELSE 0.0 END) as completion_rate
        FROM lessons l
        LEFT JOIN student_lessons sl ON l.id = sl.lesson_id
        WHERE l.course_id IN (
            SELECT c.id FROM courses c WHERE c.instructor_id = :instructorId
        )
        GROUP BY l.id, l.name
        ORDER BY l.created_at DESC
        """, nativeQuery = true)
    List<Object[]> getLessonStatisticsByInstructor(@Param("instructorId") String instructorId);

    /**
     * Find students with specific payment method
     */
    @Query("SELECT sl FROM StudentLesson sl WHERE sl.paymentMethod = :paymentMethod " +
           "AND sl.paymentDate BETWEEN :startDate AND :endDate " +
           "ORDER BY sl.paymentDate DESC")
    List<StudentLesson> findByPaymentMethodAndDateRange(@Param("paymentMethod") PaymentMethod paymentMethod,
                                                       @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);

    /**
     * Calculate total revenue for instructor
     */
    @Query(value = """
        SELECT
            COALESCE(SUM(l.price), 0) as total_revenue,
            COUNT(sl.id) as total_sales,
            COUNT(DISTINCT sl.student_id) as unique_students
        FROM student_lessons sl
        JOIN lessons l ON sl.lesson_id = l.id
        JOIN courses c ON l.course_id = c.id
        WHERE c.instructor_id = :instructorId
        AND sl.payment_date BETWEEN :startDate AND :endDate
        """, nativeQuery = true)
    Object[] calculateInstructorRevenue(@Param("instructorId") String instructorId,
                                       @Param("startDate") Date startDate,
                                       @Param("endDate") Date endDate);

    /**
     * Find duplicate access attempts (for fraud detection)
     */
    @Query(value = """
        SELECT sl.student_id, sl.lesson_id, COUNT(*) as access_count
        FROM student_lessons sl
        WHERE sl.payment_date >= :sinceDate
        GROUP BY sl.student_id, sl.lesson_id
        HAVING COUNT(*) > 1
        ORDER BY access_count DESC
        """, nativeQuery = true)
    List<Object[]> findDuplicateAccessAttempts(@Param("sinceDate") Date sinceDate);

    /**
     * Bulk extend access for multiple students
     */
    @Modifying
    @Query(value = """
        UPDATE student_lessons
        SET access_expiry_date = access_expiry_date + INTERVAL ':days days',
            updated_at = NOW()
        WHERE student_id IN :studentIds
        AND lesson_id = :lessonId
        AND access_expiry_date > NOW()
        """, nativeQuery = true)
    int bulkExtendAccess(@Param("studentIds") List<String> studentIds,
                        @Param("lessonId") String lessonId,
                        @Param("days") int days);

    /**
     * Find lessons with low completion rates
     */
    @Query(value = """
        SELECT
            l.id,
            l.name,
            COUNT(sl.id) as total_enrollments,
            COUNT(CASE WHEN sl.progress_status = 'COMPLETED' THEN 1 END) as completions,
            ROUND(
                COUNT(CASE WHEN sl.progress_status = 'COMPLETED' THEN 1 END) * 100.0 /
                NULLIF(COUNT(sl.id), 0), 2
            ) as completion_rate
        FROM lessons l
        LEFT JOIN student_lessons sl ON l.id = sl.lesson_id
        WHERE l.course_id IN (
            SELECT c.id FROM courses c WHERE c.instructor_id = :instructorId
        )
        GROUP BY l.id, l.name
        HAVING COUNT(sl.id) > :minEnrollments
        AND ROUND(
            COUNT(CASE WHEN sl.progress_status = 'COMPLETED' THEN 1 END) * 100.0 /
            NULLIF(COUNT(sl.id), 0), 2
        ) < :maxCompletionRate
        ORDER BY completion_rate ASC
        """, nativeQuery = true)
    List<Object[]> findLessonsWithLowCompletionRate(@Param("instructorId") String instructorId,
                                                   @Param("minEnrollments") int minEnrollments,
                                                   @Param("maxCompletionRate") double maxCompletionRate);
}