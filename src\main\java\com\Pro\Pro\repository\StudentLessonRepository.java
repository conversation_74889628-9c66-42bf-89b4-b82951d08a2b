package com.Pro.Pro.repository;

import com.Pro.Pro.model.StudentLesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface StudentLessonRepository extends JpaRepository<StudentLesson, String> {
    List<StudentLesson> findByStudentId(String studentId);
    boolean existsByStudentIdAndLessonId(String studentId, String lessonId);
    Long countByLessonCourseInstructorId(String instructorId);

    boolean existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
            String studentId,
            String lessonId,
            Date currentDate
    );


    @Modifying
    @Query("DELETE FROM StudentLesson sl WHERE sl.student.id = :studentId AND sl.lesson.id = :lessonId")
    void deleteByStudentIdAndLessonId(@Param("studentId") String studentId, @Param("lessonId") String lessonId);

    Optional<StudentLesson> findByStudentIdAndLessonId(String studentId, String lessonId);

    @Query(value = """
        SELECT sl.progress_status, COUNT(*) 
        FROM student_lessons sl 
        WHERE sl.lesson_id = :lessonId 
        GROUP BY sl.progress_status
        """, nativeQuery = true)
    List<Object[]> countStudentsByProgressStatus(@Param("lessonId") String lessonId);

    @Query(value = """
        UPDATE student_lessons 
        SET access_expiry_date = access_expiry_date + INTERVAL '7 days' 
        WHERE student_id = :studentId 
        AND lesson_id = :lessonId 
        AND access_expiry_date > NOW()
        """, nativeQuery = true)
    @Modifying
    void extendAccess(@Param("studentId") String studentId, @Param("lessonId") String lessonId);

    @Query("SELECT COUNT(sl) FROM StudentLesson sl WHERE sl.lesson.id = :lessonId")
    long countByLessonId(@Param("lessonId") String lessonId);
}