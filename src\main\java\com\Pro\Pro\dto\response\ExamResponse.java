package com.Pro.Pro.dto.response;

import com.Pro.Pro.service.serviceImp.ExamServiceImpl;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
public class ExamResponse {
    private String id;
    private String lessonId;
    private String title;
    private BigDecimal passingScore;
    private BigDecimal score;
    private boolean passed;
    private List<ExamQuestionResponse> questions;
    private Map<String, QuestionResult> questionResults;
    private BigDecimal maxPoints;

    public void setMaxPoints(BigDecimal maxPoints) {
        this.maxPoints = maxPoints;
    }
    private String errorMessage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLessonId() {
        return lessonId;
    }

    public void setLessonId(String lessonId) {
        this.lessonId = lessonId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BigDecimal getPassingScore() {
        return passingScore;
    }

    public void setPassingScore(BigDecimal passingScore) {
        this.passingScore = passingScore;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public boolean isPassed() {
        return passed;
    }

    public void setPassed(boolean passed) {
        this.passed = passed;
    }

    public List<ExamQuestionResponse> getQuestions() {
        return questions;
    }

    public void setQuestions(List<ExamQuestionResponse> questions) {
        this.questions = questions;
    }

    public void setQuestionResults(Map<String, QuestionResult> questionResults) {
        this.questionResults = questionResults;
    }

}
