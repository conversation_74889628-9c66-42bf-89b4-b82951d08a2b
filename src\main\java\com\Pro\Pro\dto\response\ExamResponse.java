package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
public class ExamResponse {
    private String id;
    private String lessonId;
    private String title;
    private BigDecimal passingScore;
    private BigDecimal score;
    private boolean passed;
    private List<ExamQuestionResponse> questions;
    private Map<String, QuestionResult> questionResults;
    private BigDecimal maxPoints;
    private String errorMessage;
}
