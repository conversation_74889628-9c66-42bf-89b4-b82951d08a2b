package com.Pro.Pro.service;

import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.LessonProgressStatus;
import com.Pro.Pro.service.serviceImp.LessonProgressServiceImpl;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;

public interface LessonProgressService {

    enum LessonPart {
        EXAM, VIDEO, ASSIGNMENT
    }

    void validateLessonAccess(String studentId, String lessonId, LessonPart partToAccess);

    @Transactional
    void validateLessonAccess(String studentId, String lessonId, LessonProgressServiceImpl.LessonPart partToAccess);

    void updateProgress(String studentId, String lessonId, LessonProgressStatus newStatus);
}