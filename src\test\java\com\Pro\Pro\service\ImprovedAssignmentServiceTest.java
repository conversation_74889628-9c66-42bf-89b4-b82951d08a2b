package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.AssignmentRequest;
import com.Pro.Pro.dto.response.AssignmentResponse;
import com.Pro.Pro.exception.BadRequestException;
import com.Pro.Pro.exception.ResourceNotFoundException;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.serviceImp.AssignmentServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ImprovedAssignmentServiceTest {

    @Autowired
    private AssignmentServiceImpl assignmentService;

    @Autowired
    private AssignmentRepository assignmentRepository;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private StudentAssignmentRepository studentAssignmentRepository;

    private User instructor;
    private User student;
    private Course course;
    private Lesson lesson;

    @BeforeEach
    void setUp() {
        // Set up security context for instructor
        UsernamePasswordAuthenticationToken instructorAuth = new UsernamePasswordAuthenticationToken(
                "instructor", "password", 
                Arrays.asList(new SimpleGrantedAuthority("ROLE_INSTRUCTOR"), new SimpleGrantedAuthority("ROLE_ADMIN"))
        );
        SecurityContextHolder.getContext().setAuthentication(instructorAuth);
        
        // Create instructor
        instructor = User.builder()
                .username("instructor")
                .email("<EMAIL>")
                .password("password")
                .role(Role.INSTRUCTOR)
                .fullname("Test Instructor")
                .build();
        instructor = userRepository.save(instructor);

        // Create student
        student = User.builder()
                .username("student")
                .email("<EMAIL>")
                .password("password")
                .role(Role.STUDENT)
                .fullname("Test Student")
                .build();
        student = userRepository.save(student);

        // Create course
        course = Course.builder()
                .name("Test Course")
                .description("Test Description")
                .instructor(instructor)
                .build();
        course = courseRepository.save(course);

        // Create lesson
        lesson = Lesson.builder()
                .name("Test Lesson")
                .description("Test Lesson Description")
                .price(new BigDecimal("100.00"))
                .videoUrl("http://test.com/video")
                .course(course)
                .build();
        lesson = lessonRepository.save(lesson);
    }

    @Test
    public void testCreateAssignmentWithValidData() {
        System.out.println("🧪 اختبار إنشاء واجب بيانات صحيحة...");
        
        // Create future due date
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        Date futureDate = calendar.getTime();

        AssignmentRequest request = AssignmentRequest.builder()
                .title("واجب اختبار")
                .description("وصف الواجب الاختباري")
                .dueDate(futureDate)
                .maxPoints(new BigDecimal("100.00"))
                .build();

        AssignmentResponse response = assignmentService.createAssignment(request, lesson.getId());

        assertNotNull(response);
        assertNotNull(response.getId());
        assertEquals("واجب اختبار", response.getTitle());
        assertEquals("وصف الواجب الاختباري", response.getDescription());
        assertEquals(futureDate, response.getDueDate());
        assertEquals(new BigDecimal("100.00"), response.getMaxPoints());
        
        System.out.println("✅ تم إنشاء الواجب بنجاح: " + response.getId());
    }

    @Test
    public void testCreateAssignmentWithInvalidTitle() {
        System.out.println("🧪 اختبار إنشاء واجب بعنوان غير صحيح...");
        
        AssignmentRequest request = AssignmentRequest.builder()
                .title("") // Empty title
                .description("وصف صحيح")
                .maxPoints(new BigDecimal("100.00"))
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            assignmentService.createAssignment(request, lesson.getId());
        });

        assertTrue(exception.getMessage().contains("عنوان الواجب مطلوب"));
        System.out.println("✅ تم اكتشاف العنوان الفارغ: " + exception.getMessage());
    }

    @Test
    public void testCreateAssignmentWithShortTitle() {
        System.out.println("🧪 اختبار إنشاء واجب بعنوان قصير...");
        
        AssignmentRequest request = AssignmentRequest.builder()
                .title("قص") // Too short
                .description("وصف صحيح")
                .maxPoints(new BigDecimal("100.00"))
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            assignmentService.createAssignment(request, lesson.getId());
        });

        assertTrue(exception.getMessage().contains("3 أحرف على الأقل"));
        System.out.println("✅ تم اكتشاف العنوان القصير: " + exception.getMessage());
    }

    @Test
    public void testCreateAssignmentWithInvalidPoints() {
        System.out.println("🧪 اختبار إنشاء واجب بنقاط غير صحيحة...");
        
        AssignmentRequest request = AssignmentRequest.builder()
                .title("واجب صحيح")
                .description("وصف صحيح")
                .maxPoints(new BigDecimal("-10.00")) // Negative points
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            assignmentService.createAssignment(request, lesson.getId());
        });

        assertTrue(exception.getMessage().contains("أكبر من صفر"));
        System.out.println("✅ تم اكتشاف النقاط السالبة: " + exception.getMessage());
    }

    @Test
    public void testCreateAssignmentWithPastDueDate() {
        System.out.println("🧪 اختبار إنشاء واجب بتاريخ انتهاء في الماضي...");
        
        // Create past due date
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date pastDate = calendar.getTime();

        AssignmentRequest request = AssignmentRequest.builder()
                .title("واجب صحيح")
                .description("وصف صحيح")
                .dueDate(pastDate)
                .maxPoints(new BigDecimal("100.00"))
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            assignmentService.createAssignment(request, lesson.getId());
        });

        assertTrue(exception.getMessage().contains("في المستقبل"));
        System.out.println("✅ تم اكتشاف التاريخ في الماضي: " + exception.getMessage());
    }

    @Test
    public void testCreateAssignmentWithNonExistentLesson() {
        System.out.println("🧪 اختبار إنشاء واجب لدرس غير موجود...");
        
        AssignmentRequest request = AssignmentRequest.builder()
                .title("واجب صحيح")
                .description("وصف صحيح")
                .maxPoints(new BigDecimal("100.00"))
                .build();

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            assignmentService.createAssignment(request, "nonexistent-lesson-id");
        });

        assertTrue(exception.getMessage().contains("الدرس غير موجود"));
        System.out.println("✅ تم اكتشاف الدرس غير الموجود: " + exception.getMessage());
    }

    @Test
    public void testGetAssignmentWithValidId() {
        System.out.println("🧪 اختبار استرجاع واجب بمعرف صحيح...");
        
        // First create an assignment
        Assignment assignment = Assignment.builder()
                .lesson(lesson)
                .title("واجب للاسترجاع")
                .description("وصف الواجب")
                .maxPoints(new BigDecimal("50.00"))
                .build();
        assignment = assignmentRepository.save(assignment);

        AssignmentResponse response = assignmentService.getAssignment(assignment.getId());

        assertNotNull(response);
        assertEquals(assignment.getId(), response.getId());
        assertEquals("واجب للاسترجاع", response.getTitle());
        assertNotNull(response.getIsOverdue());
        assertNotNull(response.getTimeRemaining());
        assertNotNull(response.getSubmissionCount());
        
        System.out.println("✅ تم استرجاع الواجب بنجاح: " + response.getId());
        System.out.println("📊 عدد التسليمات: " + response.getSubmissionCount());
        System.out.println("⏰ الوقت المتبقي: " + response.getTimeRemaining());
    }

    @Test
    public void testGetAssignmentWithInvalidId() {
        System.out.println("🧪 اختبار استرجاع واجب بمعرف غير صحيح...");

        // Security should deny access before business logic validation
        AuthorizationDeniedException exception = assertThrows(AuthorizationDeniedException.class, () -> {
            assignmentService.getAssignment("nonexistent-assignment-id");
        });

        assertEquals("Access Denied", exception.getMessage());
        System.out.println("✅ تم رفض الوصول للمعرف غير الصحيح: " + exception.getMessage());
    }

    @Test
    public void testGetAssignmentWithEmptyId() {
        System.out.println("🧪 اختبار استرجاع واجب بمعرف فارغ...");

        // Security should deny access before business logic validation
        AuthorizationDeniedException exception = assertThrows(AuthorizationDeniedException.class, () -> {
            assignmentService.getAssignment("");
        });

        assertEquals("Access Denied", exception.getMessage());
        System.out.println("✅ تم رفض الوصول للمعرف الفارغ: " + exception.getMessage());
    }

    @Test
    public void testAssignmentTimeCalculations() {
        System.out.println("🧪 اختبار حسابات الوقت للواجب...");
        
        // Create assignment with future due date
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, 2); // 2 hours from now
        Date futureDate = calendar.getTime();

        Assignment assignment = Assignment.builder()
                .lesson(lesson)
                .title("واجب مع موعد نهائي")
                .description("وصف الواجب")
                .dueDate(futureDate)
                .maxPoints(new BigDecimal("75.00"))
                .build();
        assignment = assignmentRepository.save(assignment);

        AssignmentResponse response = assignmentService.getAssignment(assignment.getId());

        assertNotNull(response.getIsOverdue());
        assertFalse(response.getIsOverdue()); // Should not be overdue
        assertNotNull(response.getTimeRemaining());
        assertTrue(response.getTimeRemaining().contains("ساعة")); // Should contain "hour" in Arabic
        
        System.out.println("✅ حسابات الوقت صحيحة");
        System.out.println("⏰ الوقت المتبقي: " + response.getTimeRemaining());
        System.out.println("📅 منتهي الصلاحية: " + response.getIsOverdue());
    }

    @Test
    public void testAssignmentWithSubmissions() {
        System.out.println("🧪 اختبار واجب مع تسليمات...");
        
        // Create assignment
        Assignment assignment = Assignment.builder()
                .lesson(lesson)
                .title("واجب مع تسليمات")
                .description("وصف الواجب")
                .maxPoints(new BigDecimal("100.00"))
                .build();
        assignment = assignmentRepository.save(assignment);

        // Create student submission
        StudentAssignment submission = StudentAssignment.builder()
                .student(student)
                .assignment(assignment)
                .submissionText("نص التسليم الاختباري")
                .submissionDate(new Date())
                .grade(new BigDecimal("85.00"))
                .build();
        studentAssignmentRepository.save(submission);

        AssignmentResponse response = assignmentService.getAssignment(assignment.getId());

        assertNotNull(response);
        assertEquals(1, response.getSubmissionCount());
        assertNotNull(response.getSubmissions());
        assertEquals(1, response.getSubmissions().size());
        assertNotNull(response.getAverageGrade());
        assertEquals(new BigDecimal("85.00"), response.getAverageGrade());
        assertEquals(1, response.getGradedCount());
        
        System.out.println("✅ تم اختبار الواجب مع التسليمات بنجاح");
        System.out.println("📊 عدد التسليمات: " + response.getSubmissionCount());
        System.out.println("📈 متوسط الدرجات: " + response.getAverageGrade());
        System.out.println("✅ عدد التسليمات المُقيمة: " + response.getGradedCount());
    }
}
