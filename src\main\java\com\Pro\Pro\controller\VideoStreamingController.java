//package com.Pro.Pro.controller;
//
//import com.Pro.Pro.security.CurrentUser;
//import com.Pro.Pro.security.UserDetailsImpl;
//import com.Pro.Pro.security.UserPrincipal;
//import com.Pro.Pro.service.VideoStreamingService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.io.Resource;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.MediaTypeFactory;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//
//
//@RestController
//@RequestMapping("/api/videos")
//@RequiredArgsConstructor
//public class VideoStreamingController {
//    @Autowired
//    private  VideoStreamingService videoStreamingService;
//
//    @GetMapping("/stream/{lessonId}")
//    public ResponseEntity<Resource> streamVideo(
//            @PathVariable Long lessonId,
//            @RequestParam String videoUrl,
//            @CurrentUser UserDetailsImpl currentUser) {
//
//        Resource videoResource = videoStreamingService.loadVideo(videoUrl, lessonId, currentUser.getId());
//
//        return ResponseEntity.ok()
//                .contentType(MediaTypeFactory.getMediaType(videoResource)
//                        .orElse(MediaType.APPLICATION_OCTET_STREAM))
//                .header(HttpHeaders.CONTENT_DISPOSITION,
//                        "inline; filename=\"" + videoResource.getFilename() + "\"")
//                .body(videoResource);
//    }
//
//    @GetMapping("/{lessonId}/access")
//    public ResponseEntity<Boolean> checkVideoAccess(
//            @PathVariable Long lessonId,
//            @CurrentUser UserDetailsImpl currentUser) {
//        return ResponseEntity.ok(videoStreamingService.hasVideoAccess(lessonId, currentUser.getId()));
//    }
//}