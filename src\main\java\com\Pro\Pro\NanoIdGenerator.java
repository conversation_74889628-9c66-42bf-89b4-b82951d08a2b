package com.Pro.Pro;


import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import org.hibernate.MappingException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.service.ServiceRegistry;
import org.hibernate.type.Type;

import java.io.Serializable;
import java.security.SecureRandom;
import java.util.Properties;

public class NanoIdGenerator implements IdentifierGenerator {

    private static final SecureRandom random = new SecureRandom();
    private static final char[] DEFAULT_ALPHABET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray();
    private static final int DEFAULT_SIZE = 9;

    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) {
        return NanoIdUtils.randomNanoId(random, DEFAULT_ALPHABET, DEFAULT_SIZE);
    }

    @Override
    public void configure(Type type, Properties params, ServiceRegistry serviceRegistry) throws MappingException {
        // No configuration needed for this generator
    }
}



