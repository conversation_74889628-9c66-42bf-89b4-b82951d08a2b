package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.LoginRequest;
import com.Pro.Pro.dto.request.SignupRequest;
import com.Pro.Pro.dto.response.JwtResponse;
import com.Pro.Pro.dto.response.UserResponse;
import com.Pro.Pro.security.UserDetailsImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public interface AuthService {
    JwtResponse authenticateUser(LoginRequest loginRequest, HttpServletResponse response);
    UserResponse registerStudent(SignupRequest signUpRequest);
    void logout(HttpServletRequest request, HttpServletResponse response);
    UserResponse getCurrentUser(UserDetailsImpl userDetails);
    JwtResponse refreshToken(String token, HttpServletResponse response);
}