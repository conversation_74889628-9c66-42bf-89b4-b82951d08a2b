package com.Pro.Pro.repository;

import com.Pro.Pro.model.Exam;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExamRepository extends JpaRepository<Exam, String> {

    Optional<Exam> findByLessonId(String lessonId);

    boolean existsByLessonId(String lessonId);

    @EntityGraph(attributePaths = {"questions", "questions.answers"})
    Optional<Exam> findWithQuestionsAndAnswersById(String examId);

    @Query("SELECT DISTINCT e FROM Exam e "
            + "LEFT JOIN FETCH e.questions q "
            + "LEFT JOIN FETCH q.answers a "
            + "WHERE e.id = :examId "
            + "ORDER BY q.questionOrder, a.answerOrder")
    Optional<Exam> findExamWithQuestionsAndAnswers(@Param("examId") String examId);

    @Query(value = """
        SELECT eq.id as question_id, eq.question_text, eq.question_type, 
               COUNT(ea.id) as answer_count 
        FROM exam_questions eq 
        LEFT JOIN exam_answers ea ON eq.id = ea.question_id 
        WHERE eq.exam_id = :examId 
        GROUP BY eq.id
        """, nativeQuery = true)
    List<Object[]> findExamQuestionStats(@Param("examId") String examId);

    @Query("SELECT AVG(sa.grade) FROM StudentAssignment sa "
            + "WHERE sa.assignment.lesson.id = (SELECT e.lesson.id FROM Exam e WHERE e.id = :examId)")
    Optional<Double> findAverageGradeForExamLesson(@Param("examId") String examId);
}
