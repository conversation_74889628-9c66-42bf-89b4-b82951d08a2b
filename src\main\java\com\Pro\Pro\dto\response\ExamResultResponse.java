package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class ExamResultResponse {
    private String examId;
    private String title;
    private long totalSubmissions;
    private long passedCount;
    private BigDecimal passRate;
    private BigDecimal averageScore;
    private List<QuestionStats> questionStats;
    private String message;

    @Data
    @Builder
    public static class QuestionStats {
        private String questionId;
        private String questionText;
        private BigDecimal averageScore;
        private long correctCount;
        private long totalAttempts;
    }
}
