package com.Pro.Pro.exception;

import com.Pro.Pro.dto.response.ErrorResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Object> handleResourceNotFoundException(
            ResourceNotFoundException ex, WebRequest request) {
        log.warn("مورد غير موجود: {}", ex.getMessage());
        return buildArabicErrorResponse(ex.getMessage(), "المورد المطلوب غير موجود", HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<Object> handleBadRequestException(
            BadRequestException ex, WebRequest request) {
        log.warn("طلب غير صحيح: {}", ex.getMessage());
        return buildArabicErrorResponse(ex.getMessage(), "طلب غير صحيح", HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ConflictException.class)
    public ResponseEntity<Object> handleConflictException(
            ConflictException ex, WebRequest request) {
        log.warn("تعارض في البيانات: {}", ex.getMessage());
        return buildArabicErrorResponse(ex.getMessage(), "تعارض في البيانات", HttpStatus.CONFLICT);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Object> handleAccessDeniedException(
            AccessDeniedException ex, WebRequest request) {
        log.warn("وصول مرفوض: {}", ex.getMessage());
        return buildArabicErrorResponse("ليس لديك صلاحية للوصول لهذا المورد", "وصول مرفوض", HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(org.springframework.security.access.AccessDeniedException.class)
    public ResponseEntity<Object> handleSpringAccessDeniedException(
            org.springframework.security.access.AccessDeniedException ex, WebRequest request) {
        log.warn("وصول مرفوض من Spring Security: {}", ex.getMessage());
        return buildArabicErrorResponse("ليس لديك صلاحية للوصول لهذا المورد", "وصول مرفوض", HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(ServiceException.class)
    public ResponseEntity<Object> handleServiceException(
            ServiceException ex, WebRequest request) {
        log.error("خطأ في الخدمة: {}", ex.getMessage(), ex);
        return buildArabicErrorResponse(ex.getMessage(), "خطأ في الخدمة", ex.getHttpStatus());
    }

    @ExceptionHandler(LessonNotPurchasedException.class)
    public ResponseEntity<Object> handleLessonNotPurchasedException(
            LessonNotPurchasedException ex, WebRequest request) {
        log.warn("درس غير مشترى: {}", ex.getMessage());
        return buildArabicErrorResponse(ex.getMessage(), "يجب شراء الدرس أولاً", HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(VideoNotWatchedException.class)
    public ResponseEntity<Object> handleVideoNotWatchedException(
            VideoNotWatchedException ex, WebRequest request) {
        log.warn("فيديو غير مشاهد: {}", ex.getMessage());
        return buildArabicErrorResponse(ex.getMessage(), "يجب مشاهدة الفيديو أولاً", HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ExamNotPassedException.class)
    public ResponseEntity<Object> handleExamNotPassedException(
            ExamNotPassedException ex, WebRequest request) {
        log.warn("امتحان غير ناجح: {}", ex.getMessage());
        return buildArabicErrorResponse(ex.getMessage(), "لم تنجح في الامتحان", HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {
        log.warn("خطأ في التحقق من صحة البيانات: {}", ex.getMessage());

        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        Map<String, Object> body = new LinkedHashMap<>();
        body.put("timestamp", LocalDateTime.now());
        body.put("status", HttpStatus.BAD_REQUEST.value());
        body.put("error", "بيانات غير صحيحة");
        body.put("message", "يرجى التحقق من البيانات المدخلة");
        body.put("errors", errors);
        body.put("path", request.getDescription(false));

        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleAllExceptions(
            Exception ex, WebRequest request) {
        log.error("خطأ غير متوقع: {}", ex.getMessage(), ex);
        return buildArabicErrorResponse("حدث خطأ غير متوقع. يرجى المحاولة لاحقاً", "خطأ في الخادم", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // =============== Helper Methods ===============

    private ResponseEntity<Object> buildArabicErrorResponse(String message, String error, HttpStatus status) {
        Map<String, Object> body = new LinkedHashMap<>();
        body.put("timestamp", LocalDateTime.now());
        body.put("status", status.value());
        body.put("error", error);
        body.put("message", message);
        body.put("path", ""); // Can be enhanced to include request path

        return new ResponseEntity<>(body, status);
    }

    private ResponseEntity<Object> buildErrorResponse(RuntimeException ex, HttpStatus status) {
        Map<String, Object> body = new LinkedHashMap<>();
        body.put("timestamp", LocalDateTime.now());
        body.put("message", ex.getMessage());

        return new ResponseEntity<>(body, status);
    }
}