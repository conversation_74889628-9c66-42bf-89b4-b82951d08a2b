package com.Pro.Pro.dto.request;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ExamAnswerUpdateRequest {

    private String id; // null for new answers
    private String answerText;
    private boolean correct;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAnswerText() {
        return answerText;
    }

    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }

    public boolean isCorrect() {
        return correct;
    }

    public void setCorrect(boolean correct) {
        this.correct = correct;
    }
}
