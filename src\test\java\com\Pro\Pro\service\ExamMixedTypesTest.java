package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.Pro.Pro.dto.request.ExamQuestionRequest;
import com.Pro.Pro.dto.request.ExamRequest;
import com.Pro.Pro.exception.BadRequestException;
import com.Pro.Pro.model.QuestionType;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class ExamMixedTypesTest {

    @Test
    public void testValidMixedTypeExam() {
        ExamRequest examRequest = createValidMixedTypeExam();
        
        assertNotNull(examRequest);
        assertEquals("Mixed Type Exam", examRequest.getTitle());
        assertEquals(new BigDecimal("75.00"), examRequest.getPassingScore());
        assertEquals(60, examRequest.getTimeLimitMinutes());
        assertEquals(4, examRequest.getQuestions().size());
        
        // Verify question types
        List<ExamQuestionRequest> questions = examRequest.getQuestions();
        assertEquals(QuestionType.MULTIPLE_CHOICE, questions.get(0).getQuestionType());
        assertEquals(QuestionType.SINGLE_CHOICE, questions.get(1).getQuestionType());
        assertEquals(QuestionType.TRUE_FALSE, questions.get(2).getQuestionType());
        assertEquals(QuestionType.MULTIPLE_CHOICE, questions.get(3).getQuestionType());
        
        // Verify total points
        BigDecimal totalPoints = questions.stream()
                .map(ExamQuestionRequest::getPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("100.00"), totalPoints);
    }

    @Test
    public void testInvalidTrueFalseQuestion() {
        // Test TRUE_FALSE with wrong number of answers
        assertThrows(RuntimeException.class, () -> {
            ExamQuestionRequest.builder()
                .questionText("Invalid true/false question")
                .questionType(QuestionType.TRUE_FALSE)
                .points(new BigDecimal("10.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("false").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("maybe").correct(false).build() // Invalid
                ))
                .build();
        });
    }

    @Test
    public void testInvalidSingleChoiceQuestion() {
        // Test SINGLE_CHOICE with multiple correct answers
        assertThrows(RuntimeException.class, () -> {
            ExamQuestionRequest.builder()
                .questionText("Invalid single choice question")
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(new BigDecimal("10.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Answer 1").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Answer 2").correct(true).build(), // Invalid - multiple correct
                    ExamAnswerRequest.builder().answerText("Answer 3").correct(false).build()
                ))
                .build();
        });
    }

    @Test
    public void testInvalidMultipleChoiceQuestion() {
        // Test MULTIPLE_CHOICE with no correct answers
        assertThrows(RuntimeException.class, () -> {
            ExamQuestionRequest.builder()
                .questionText("Invalid multiple choice question")
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("10.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Answer 1").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Answer 2").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Answer 3").correct(false).build() // No correct answers
                ))
                .build();
        });
    }

    @Test
    public void testLargeExamWithAllTypes() {
        ExamRequest largeExam = createLargeExamWithAllTypes();
        
        assertNotNull(largeExam);
        assertEquals(15, largeExam.getQuestions().size());
        
        // Count question types
        long multipleChoiceCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.MULTIPLE_CHOICE)
                .count();
        long singleChoiceCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.SINGLE_CHOICE)
                .count();
        long trueFalseCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.TRUE_FALSE)
                .count();
        
        assertEquals(5, multipleChoiceCount);
        assertEquals(5, singleChoiceCount);
        assertEquals(5, trueFalseCount);
        
        // Verify total points
        BigDecimal totalPoints = largeExam.getQuestions().stream()
                .map(ExamQuestionRequest::getPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("300.00"), totalPoints);
    }

    @Test
    public void testExamWithMinimumQuestions() {
        // Test exam with just one question
        ExamRequest singleQuestionExam = ExamRequest.builder()
                .title("Single Question Exam")
                .passingScore(new BigDecimal("50.00"))
                .timeLimitMinutes(15)
                .questions(Arrays.asList(
                    ExamQuestionRequest.builder()
                        .questionText("Is Java object-oriented?")
                        .questionType(QuestionType.TRUE_FALSE)
                        .points(new BigDecimal("100.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("false").correct(false).build()
                        ))
                        .build()
                ))
                .build();
        
        assertNotNull(singleQuestionExam);
        assertEquals(1, singleQuestionExam.getQuestions().size());
        assertEquals(new BigDecimal("100.00"), singleQuestionExam.getQuestions().get(0).getPoints());
    }

    private ExamRequest createValidMixedTypeExam() {
        List<ExamQuestionRequest> questions = Arrays.asList(
            // Multiple choice question
            ExamQuestionRequest.builder()
                .questionText("Which are programming languages?")
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("25.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Java").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Python").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("HTML").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("CSS").correct(false).build()
                ))
                .build(),
            
            // Single choice question
            ExamQuestionRequest.builder()
                .questionText("What is the capital of France?")
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(new BigDecimal("25.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Paris").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("London").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Berlin").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Madrid").correct(false).build()
                ))
                .build(),
            
            // True/False question
            ExamQuestionRequest.builder()
                .questionText("Java is platform independent.")
                .questionType(QuestionType.TRUE_FALSE)
                .points(new BigDecimal("25.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("false").correct(false).build()
                ))
                .build(),
            
            // Another multiple choice question
            ExamQuestionRequest.builder()
                .questionText("Which are web technologies?")
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("25.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("HTML").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("CSS").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("JavaScript").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("MySQL").correct(false).build()
                ))
                .build()
        );
        
        return ExamRequest.builder()
                .title("Mixed Type Exam")
                .passingScore(new BigDecimal("75.00"))
                .timeLimitMinutes(60)
                .questions(questions)
                .build();
    }

    private ExamRequest createLargeExamWithAllTypes() {
        List<ExamQuestionRequest> questions = new java.util.ArrayList<>();
        
        // Add 5 multiple choice questions
        for (int i = 1; i <= 5; i++) {
            questions.add(ExamQuestionRequest.builder()
                .questionText("Multiple choice question " + i)
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("20.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct A").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Correct B").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong A").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong B").correct(false).build()
                ))
                .build());
        }
        
        // Add 5 single choice questions
        for (int i = 1; i <= 5; i++) {
            questions.add(ExamQuestionRequest.builder()
                .questionText("Single choice question " + i)
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(new BigDecimal("20.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct answer").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong answer 1").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong answer 2").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong answer 3").correct(false).build()
                ))
                .build());
        }
        
        // Add 5 true/false questions
        for (int i = 1; i <= 5; i++) {
            questions.add(ExamQuestionRequest.builder()
                .questionText("True/False question " + i)
                .questionType(QuestionType.TRUE_FALSE)
                .points(new BigDecimal("20.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("true").correct(i % 2 == 1).build(),
                    ExamAnswerRequest.builder().answerText("false").correct(i % 2 == 0).build()
                ))
                .build());
        }
        
        return ExamRequest.builder()
                .title("Large Mixed Type Exam")
                .passingScore(new BigDecimal("70.00"))
                .timeLimitMinutes(180)
                .questions(questions)
                .build();
    }
}
