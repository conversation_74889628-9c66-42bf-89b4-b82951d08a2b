package com.Pro.Pro.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.util.UUID;

@Entity
@Table(name = "exam_answers", indexes = {
        @Index(name = "idx_exam_answer_question", columnList = "question_id"),
        @Index(name = "idx_exam_answer_correct", columnList = "isCorrect")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class ExamAnswer extends Auditable {
    @Id
    @GeneratedValue(generator = "nanoid")
    @GenericGenerator(name = "nanoid", strategy = "com.Pro.Pro.NanoIdGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private String id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", nullable = false)
    @ToString.Exclude
    private ExamQuestion question;

    private String answerText;
    private boolean isCorrect;
    private Integer answerOrder;
}