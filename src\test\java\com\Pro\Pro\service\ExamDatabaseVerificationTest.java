package com.Pro.Pro.service;

import com.Pro.Pro.model.Exam;
import com.Pro.Pro.model.ExamAnswer;
import com.Pro.Pro.model.ExamQuestion;
import com.Pro.Pro.repository.ExamRepository;
import com.Pro.Pro.repository.ExamQuestionRepository;
import com.Pro.Pro.repository.ExamAnswerRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ExamDatabaseVerificationTest {

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private ExamQuestionRepository examQuestionRepository;

    @Autowired
    private ExamAnswerRepository examAnswerRepository;

    @Test
    public void testDatabaseContents() {
        // Get all exams
        List<Exam> allExams = examRepository.findAll();
        System.out.println("📊 Total exams in database: " + allExams.size());

        for (Exam exam : allExams) {
            System.out.println("\n🔍 Exam: " + exam.getId() + " - " + exam.getTitle());
            System.out.println("   Max Points: " + exam.getMaxPoints());
            System.out.println("   Passing Score: " + exam.getPassingScore());

            // Get questions for this exam
            List<ExamQuestion> questions = examQuestionRepository.findByExamId(exam.getId());
            System.out.println("   📝 Questions: " + questions.size());

            for (ExamQuestion question : questions) {
                System.out.println("     Question " + question.getQuestionOrder() + ": " + question.getId());
                System.out.println("       Text: " + question.getQuestionText());
                System.out.println("       Type: " + question.getQuestionType());
                System.out.println("       Points: " + question.getPoints());

                // Get answers for this question
                List<ExamAnswer> answers = examAnswerRepository.findByQuestionId(question.getId());
                System.out.println("       💡 Answers: " + answers.size());

                for (ExamAnswer answer : answers) {
                    System.out.println("         Answer " + answer.getAnswerOrder() + ": " + answer.getId());
                    System.out.println("           Text: " + answer.getAnswerText());
                    System.out.println("           Correct: " + answer.isCorrect());
                }
            }
        }
    }

    @Test
    public void testSpecificExamRetrieval() {
        // Test the specific exam ID from the user's request
        String examId = "ryRW7J17N"; // This might not exist in test DB, but let's try

        // Try different retrieval methods
        System.out.println("🔍 Testing exam retrieval for ID: " + examId);

        // Method 1: Simple findById
        Optional<Exam> exam1 = examRepository.findById(examId);
        System.out.println("Method 1 (findById): " + (exam1.isPresent() ? "Found" : "Not found"));

        if (exam1.isPresent()) {
            System.out.println("  Questions loaded: " + exam1.get().getQuestions().size());
        }

        // Method 2: findExamWithQuestionsAndAnswers (JOIN FETCH - problematic)
        Optional<Exam> exam2 = examRepository.findExamWithQuestionsAndAnswers(examId);
        System.out.println("Method 2 (findExamWithQuestionsAndAnswers - JOIN FETCH): " + (exam2.isPresent() ? "Found" : "Not found"));

        if (exam2.isPresent()) {
            System.out.println("  Questions loaded: " + exam2.get().getQuestions().size());
            for (ExamQuestion question : exam2.get().getQuestions()) {
                System.out.println("    Question: " + question.getQuestionText());
                System.out.println("    Answers: " + question.getAnswers().size());
            }
        }

        // Method 3: findWithQuestionsAndAnswersById
        Optional<Exam> exam3 = examRepository.findWithQuestionsAndAnswersById(examId);
        System.out.println("Method 3 (findWithQuestionsAndAnswersById): " + (exam3.isPresent() ? "Found" : "Not found"));

        if (exam3.isPresent()) {
            System.out.println("  Questions loaded: " + exam3.get().getQuestions().size());
        }
    }

    @Test
    public void testExamCreationAndRetrieval() {
        // This test will verify that our exam creation and retrieval works correctly
        System.out.println("🧪 Testing exam creation and retrieval...");

        // Get the most recent exam (if any)
        List<Exam> allExams = examRepository.findAll();
        if (!allExams.isEmpty()) {
            Exam latestExam = allExams.get(allExams.size() - 1);
            String examId = latestExam.getId();

            System.out.println("📋 Testing with exam: " + examId + " - " + latestExam.getTitle());

            // Test the repository method used by the service
            Optional<Exam> retrievedExam = examRepository.findExamWithQuestionsAndAnswers(examId);

            if (retrievedExam.isPresent()) {
                Exam exam = retrievedExam.get();
                System.out.println("✅ Successfully retrieved exam");
                System.out.println("   Title: " + exam.getTitle());
                System.out.println("   Questions: " + exam.getQuestions().size());

                for (ExamQuestion question : exam.getQuestions()) {
                    System.out.println("   📝 Question: " + question.getQuestionText());
                    System.out.println("      Type: " + question.getQuestionType());
                    System.out.println("      Points: " + question.getPoints());
                    System.out.println("      Answers: " + question.getAnswers().size());

                    for (ExamAnswer answer : question.getAnswers()) {
                        System.out.println("        💡 " + answer.getAnswerText() + " (Correct: " + answer.isCorrect() + ")");
                    }
                }

                // Verify the data integrity
                assertTrue(exam.getQuestions().size() > 0, "Exam should have questions");

                for (ExamQuestion question : exam.getQuestions()) {
                    assertNotNull(question.getQuestionText(), "Question text should not be null");
                    assertNotNull(question.getQuestionType(), "Question type should not be null");
                    assertNotNull(question.getPoints(), "Question points should not be null");
                    assertTrue(question.getAnswers().size() > 0, "Question should have answers");

                    for (ExamAnswer answer : question.getAnswers()) {
                        assertNotNull(answer.getAnswerText(), "Answer text should not be null");
                        // Note: isCorrect can be false, so we don't assert it's true
                    }
                }

                System.out.println("✅ All data integrity checks passed!");

            } else {
                System.out.println("❌ Failed to retrieve exam");
                fail("Could not retrieve exam with questions and answers");
            }
        } else {
            System.out.println("ℹ️ No exams found in database");
        }
    }
}
