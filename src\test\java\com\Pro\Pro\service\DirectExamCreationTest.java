package com.Pro.Pro.service;

import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DirectExamCreationTest {

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private ExamQuestionRepository examQuestionRepository;

    @Autowired
    private ExamAnswerRepository examAnswerRepository;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CourseRepository courseRepository;

    private Lesson lesson;

    @BeforeEach
    void setUp() {
        // Create instructor
        User instructor = User.builder()
                .username("instructor")
                .email("<EMAIL>")
                .password("password")
                .role(Role.INSTRUCTOR)
                .build();
        instructor = userRepository.save(instructor);

        // Create course
        Course course = Course.builder()
                .name("Test Course")
                .description("Test Description")
                .instructor(instructor)
                .build();
        course = courseRepository.save(course);

        // Create lesson
        lesson = Lesson.builder()
                .name("Test Lesson")
                .description("Test Lesson Description")
                .price(new BigDecimal("100.00"))
                .videoUrl("http://test.com/video")
                .course(course)
                .build();
        lesson = lessonRepository.save(lesson);
    }

    @Test
    public void testDirectExamCreation() {
        System.out.println("🧪 Testing direct exam creation...");
        
        // Step 1: Create and save exam
        Exam exam = Exam.builder()
                .lesson(lesson)
                .title("Direct Test Exam")
                .passingScore(new BigDecimal("70.00"))
                .timeLimitMinutes(60)
                .maxPoints(new BigDecimal("45.00"))
                .build();
        
        exam = examRepository.save(exam);
        System.out.println("✅ Exam saved with ID: " + exam.getId());
        
        // Step 2: Create and save questions directly
        Set<ExamQuestion> questions = new LinkedHashSet<>();
        
        // Question 1: Multiple Choice
        ExamQuestion q1 = ExamQuestion.builder()
                .exam(exam)
                .questionText("Direct MC Question")
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("20.00"))
                .questionOrder(1)
                .build();
        q1 = examQuestionRepository.save(q1);
        System.out.println("✅ Question 1 saved with ID: " + q1.getId());
        
        // Add answers for Q1
        Set<ExamAnswer> q1Answers = new LinkedHashSet<>();
        
        ExamAnswer q1a1 = ExamAnswer.builder()
                .question(q1)
                .answerText("MC Answer 1")
                .isCorrect(true)
                .answerOrder(1)
                .build();
        q1a1 = examAnswerRepository.save(q1a1);
        q1Answers.add(q1a1);
        
        ExamAnswer q1a2 = ExamAnswer.builder()
                .question(q1)
                .answerText("MC Answer 2")
                .isCorrect(true)
                .answerOrder(2)
                .build();
        q1a2 = examAnswerRepository.save(q1a2);
        q1Answers.add(q1a2);
        
        ExamAnswer q1a3 = ExamAnswer.builder()
                .question(q1)
                .answerText("MC Answer 3")
                .isCorrect(false)
                .answerOrder(3)
                .build();
        q1a3 = examAnswerRepository.save(q1a3);
        q1Answers.add(q1a3);
        
        q1.setAnswers(q1Answers);
        questions.add(q1);
        System.out.println("✅ Question 1 has " + q1Answers.size() + " answers");
        
        // Question 2: Single Choice
        ExamQuestion q2 = ExamQuestion.builder()
                .exam(exam)
                .questionText("Direct SC Question")
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(new BigDecimal("15.00"))
                .questionOrder(2)
                .build();
        q2 = examQuestionRepository.save(q2);
        System.out.println("✅ Question 2 saved with ID: " + q2.getId());
        
        // Add answers for Q2
        Set<ExamAnswer> q2Answers = new LinkedHashSet<>();
        
        ExamAnswer q2a1 = ExamAnswer.builder()
                .question(q2)
                .answerText("SC Answer 1")
                .isCorrect(true)
                .answerOrder(1)
                .build();
        q2a1 = examAnswerRepository.save(q2a1);
        q2Answers.add(q2a1);
        
        ExamAnswer q2a2 = ExamAnswer.builder()
                .question(q2)
                .answerText("SC Answer 2")
                .isCorrect(false)
                .answerOrder(2)
                .build();
        q2a2 = examAnswerRepository.save(q2a2);
        q2Answers.add(q2a2);
        
        q2.setAnswers(q2Answers);
        questions.add(q2);
        System.out.println("✅ Question 2 has " + q2Answers.size() + " answers");
        
        // Question 3: True/False
        ExamQuestion q3 = ExamQuestion.builder()
                .exam(exam)
                .questionText("Direct TF Question")
                .questionType(QuestionType.TRUE_FALSE)
                .points(new BigDecimal("10.00"))
                .questionOrder(3)
                .build();
        q3 = examQuestionRepository.save(q3);
        System.out.println("✅ Question 3 saved with ID: " + q3.getId());
        
        // Add answers for Q3
        Set<ExamAnswer> q3Answers = new LinkedHashSet<>();
        
        ExamAnswer q3a1 = ExamAnswer.builder()
                .question(q3)
                .answerText("true")
                .isCorrect(true)
                .answerOrder(1)
                .build();
        q3a1 = examAnswerRepository.save(q3a1);
        q3Answers.add(q3a1);
        
        ExamAnswer q3a2 = ExamAnswer.builder()
                .question(q3)
                .answerText("false")
                .isCorrect(false)
                .answerOrder(2)
                .build();
        q3a2 = examAnswerRepository.save(q3a2);
        q3Answers.add(q3a2);
        
        q3.setAnswers(q3Answers);
        questions.add(q3);
        System.out.println("✅ Question 3 has " + q3Answers.size() + " answers");
        
        // Step 3: Update exam with questions
        exam.setQuestions(questions);
        exam = examRepository.save(exam);
        
        System.out.println("✅ Exam updated with " + questions.size() + " questions");
        
        // Step 4: Retrieve and verify
        Optional<Exam> retrievedExam = examRepository.findById(exam.getId());
        assertTrue(retrievedExam.isPresent());
        
        Exam finalExam = retrievedExam.get();
        
        // Force load questions
        finalExam.getQuestions().size();
        
        System.out.println("\n📊 Final Verification:");
        System.out.println("Exam ID: " + finalExam.getId());
        System.out.println("Title: " + finalExam.getTitle());
        System.out.println("Questions: " + finalExam.getQuestions().size());
        
        assertEquals(3, finalExam.getQuestions().size());
        
        int questionNum = 1;
        for (ExamQuestion question : finalExam.getQuestions()) {
            // Force load answers
            question.getAnswers().size();
            
            System.out.println("  Question " + questionNum + ": " + question.getQuestionText());
            System.out.println("    Type: " + question.getQuestionType());
            System.out.println("    Points: " + question.getPoints());
            System.out.println("    Answers: " + question.getAnswers().size());
            
            assertTrue(question.getAnswers().size() >= 2);
            
            questionNum++;
        }
        
        System.out.println("✅ Direct exam creation test passed!");
    }
}
