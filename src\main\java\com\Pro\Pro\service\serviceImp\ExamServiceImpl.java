package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.security.SecurityService;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.ExamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExamServiceImpl implements ExamService {

    private final ExamRepository examRepository;
    private final LessonRepository lessonRepository;
    private final ExamQuestionRepository examQuestionRepository;
    private final ExamAnswerRepository examAnswerRepository;
    private final UserRepository userRepository;
    private final LessonProgressServiceImpl progressService;
    private final SecurityService securityService;
    private final StudentExamRepository studentExamRepository;
    private final StudentExamAnswerRepository studentExamAnswerRepository;
    private final StudentLessonRepository studentLessonRepository;

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    public ExamResponse createExam(ExamRequest request, String lessonId) {
        try {
            Lesson lesson = lessonRepository.findById(lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("الدرس غير موجود"));

            if (examRepository.existsByLessonId(lessonId)) {
                throw new BadRequestException("هذا الدرس يحتوي بالفعل على امتحان");
            }

            // Validate exam request first
            validateExamRequest(request);

            // Calculate total points
            BigDecimal totalPoints = request.getQuestions().stream()
                    .map(ExamQuestionRequest::getPoints)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Create and save exam first
            Exam exam = Exam.builder()
                    .lesson(lesson)
                    .title(request.getTitle())
                    .passingScore(request.getPassingScore())
                    .timeLimitMinutes(request.getTimeLimitMinutes())
                    .maxPoints(totalPoints)
                    .build();

            exam = examRepository.save(exam);
            log.debug("Exam saved with ID: {}", exam.getId());

            // Create and save questions with their answers
            List<ExamQuestion> questions = new ArrayList<>();
            int questionOrder = 1;

            log.debug("Processing {} questions for exam", request.getQuestions().size());

            for (int i = 0; i < request.getQuestions().size(); i++) {
                ExamQuestionRequest questionReq = request.getQuestions().get(i);
                log.debug("Processing question {} of {}: {}", (i + 1), request.getQuestions().size(), questionReq.getQuestionText());

                try {
                    validateQuestion(questionReq);
                    log.debug("Question {} validation passed", (i + 1));
                } catch (BadRequestException e) {
                    log.error("Question {} validation failed: {}", (i + 1), e.getMessage());
                    throw new BadRequestException("خطأ في السؤال رقم " + (i + 1) + ": " + e.getMessage());
                }

                // Create and save question
                ExamQuestion question = ExamQuestion.builder()
                        .exam(exam)
                        .questionText(questionReq.getQuestionText())
                        .questionType(questionReq.getQuestionType())
                        .points(questionReq.getPoints())
                        .questionOrder(questionOrder++)
                        .build();

                question = examQuestionRepository.save(question);
                log.debug("Question {} saved with ID: {} (Type: {})", (i + 1), question.getId(), question.getQuestionType());

                // Create and save answers for this question
                log.debug("Creating {} answers for question {}", questionReq.getAnswers().size(), (i + 1));
                List<ExamAnswer> answers = createAndSaveAnswersForQuestion(question, questionReq);
                question.setAnswers(answers);
                questions.add(question);

                log.debug("Question {} completed. Total questions so far: {}", (i + 1), questions.size());
            }

            log.debug("Finished processing all questions. Total created: {}", questions.size());

            // Update exam with questions
            exam.setQuestions(questions);
            exam = examRepository.save(exam);

            return mapExamToResponse(exam);
        } catch (ResourceNotFoundException e) {
            throw new ResourceNotFoundException("الدرس غير موجود: " + e.getMessage());
        } catch (BadRequestException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("فشل إنشاء الامتحان: " + e.getMessage(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void validateExamRequest(ExamRequest request) {
        if (request.getQuestions() == null || request.getQuestions().isEmpty()) {
            throw new BadRequestException("يجب أن يحتوي الامتحان على سؤال واحد على الأقل");
        }

        if (request.getPassingScore().compareTo(BigDecimal.ZERO) < 0
                || request.getPassingScore().compareTo(new BigDecimal("100")) > 0) {
            throw new BadRequestException("درجة النجاح يجب أن تكون بين 0 و 100");
        }

        if (request.getTimeLimitMinutes() <= 0) {
            throw new BadRequestException("وقت الامتحان يجب أن يكون أكبر من صفر");
        }

        // Check for duplicate question texts
        Set<String> questionTexts = new HashSet<>();
        for (int i = 0; i < request.getQuestions().size(); i++) {
            ExamQuestionRequest question = request.getQuestions().get(i);
            String questionText = question.getQuestionText().trim().toLowerCase();
            if (!questionTexts.add(questionText)) {
                throw new BadRequestException("السؤال رقم " + (i + 1) + " مكرر");
            }
        }

        // Validate total points
        BigDecimal totalPoints = request.getQuestions().stream()
                .map(ExamQuestionRequest::getPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalPoints.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BadRequestException("مجموع نقاط الأسئلة يجب أن يكون أكبر من صفر");
        }

        log.debug("Exam validation passed. Total questions: {}, Total points: {}",
                request.getQuestions().size(), totalPoints);
    }

    private void validateQuestion(ExamQuestionRequest questionReq) {
        log.debug("Validating question of type: {}", questionReq.getQuestionType());
        log.debug("Question text: {}", questionReq.getQuestionText());

        if (questionReq.getAnswers() == null || questionReq.getAnswers().isEmpty()) {
            throw new BadRequestException("يجب أن يحتوي السؤال على إجابات");
        }

        // Log all answers
        questionReq.getAnswers().forEach(answer
                -> log.debug("Answer: {}, isCorrect: {}", answer.getAnswerText(), answer.isCorrect()));

        long correctAnswersCount = questionReq.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();

        log.debug("Found {} correct answers", correctAnswersCount);

        // Validate based on question type
        switch (questionReq.getQuestionType()) {
            case TRUE_FALSE:
                if (questionReq.getAnswers().size() != 2) {
                    throw new BadRequestException("أسئلة الصواب/الخطأ يجب أن تحتوي على إجابتين فقط");
                }
                if (correctAnswersCount != 1) {
                    throw new BadRequestException("أسئلة الصواب/الخطأ يجب أن تحتوي على إجابة صحيحة واحدة فقط");
                }
                // Validate that answers are "true" and "false"
                boolean hasTrue = questionReq.getAnswers().stream()
                        .anyMatch(a -> a.getAnswerText().equalsIgnoreCase("true"));
                boolean hasFalse = questionReq.getAnswers().stream()
                        .anyMatch(a -> a.getAnswerText().equalsIgnoreCase("false"));
                if (!hasTrue || !hasFalse) {
                    throw new BadRequestException("أسئلة الصواب/الخطأ يجب أن تحتوي على 'true' و 'false'");
                }
                break;

            case SINGLE_CHOICE:
                if (questionReq.getAnswers().size() < 2) {
                    throw new BadRequestException("أسئلة الاختيار الفردي يجب أن تحتوي على إجابتين على الأقل");
                }
                if (correctAnswersCount != 1) {
                    throw new BadRequestException("أسئلة الاختيار الفردي يجب أن تحتوي على إجابة صحيحة واحدة فقط");
                }
                break;

            case MULTIPLE_CHOICE:
                if (questionReq.getAnswers().size() < 2) {
                    throw new BadRequestException("أسئلة الاختيار المتعدد يجب أن تحتوي على إجابتين على الأقل");
                }
                if (correctAnswersCount < 1) {
                    throw new BadRequestException("أسئلة الاختيار المتعدد يجب أن تحتوي على إجابة صحيحة واحدة على الأقل");
                }
                if (correctAnswersCount >= questionReq.getAnswers().size()) {
                    throw new BadRequestException("أسئلة الاختيار المتعدد يجب أن تحتوي على إجابة خاطئة واحدة على الأقل");
                }
                break;

            default:
                throw new BadRequestException("نوع السؤال غير مدعوم: " + questionReq.getQuestionType());
        }
    }

    private void validateQuestionUpdate(ExamQuestionUpdateRequest questionReq) {
        if (questionReq.getAnswers() == null || questionReq.getAnswers().isEmpty()) {
            throw new BadRequestException("يجب أن يحتوي السؤال على إجابات");
        }

        long correctAnswersCount = questionReq.getAnswers().stream()
                .filter(ExamAnswerUpdateRequest::isCorrect)
                .count();

        // Validate based on question type
        switch (questionReq.getQuestionType()) {
            case TRUE_FALSE:
                if (questionReq.getAnswers().size() != 2) {
                    throw new BadRequestException("أسئلة الصواب/الخطأ يجب أن تحتوي على إجابتين فقط");
                }
                if (correctAnswersCount != 1) {
                    throw new BadRequestException("أسئلة الصواب/الخطأ يجب أن تحتوي على إجابة صحيحة واحدة فقط");
                }
                // Validate that answers are "true" and "false"
                boolean hasTrue = questionReq.getAnswers().stream()
                        .anyMatch(a -> a.getAnswerText().equalsIgnoreCase("true"));
                boolean hasFalse = questionReq.getAnswers().stream()
                        .anyMatch(a -> a.getAnswerText().equalsIgnoreCase("false"));
                if (!hasTrue || !hasFalse) {
                    throw new BadRequestException("أسئلة الصواب/الخطأ يجب أن تحتوي على 'true' و 'false'");
                }
                break;

            case SINGLE_CHOICE:
                if (questionReq.getAnswers().size() < 2) {
                    throw new BadRequestException("أسئلة الاختيار الفردي يجب أن تحتوي على إجابتين على الأقل");
                }
                if (correctAnswersCount != 1) {
                    throw new BadRequestException("أسئلة الاختيار الفردي يجب أن تحتوي على إجابة صحيحة واحدة فقط");
                }
                break;

            case MULTIPLE_CHOICE:
                if (questionReq.getAnswers().size() < 2) {
                    throw new BadRequestException("أسئلة الاختيار المتعدد يجب أن تحتوي على إجابتين على الأقل");
                }
                if (correctAnswersCount < 1) {
                    throw new BadRequestException("أسئلة الاختيار المتعدد يجب أن تحتوي على إجابة صحيحة واحدة على الأقل");
                }
                if (correctAnswersCount >= questionReq.getAnswers().size()) {
                    throw new BadRequestException("أسئلة الاختيار المتعدد يجب أن تحتوي على إجابة خاطئة واحدة على الأقل");
                }
                break;

            default:
                throw new BadRequestException("نوع السؤال غير مدعوم: " + questionReq.getQuestionType());
        }
    }


    @Override
    @Transactional
    @PreAuthorize("@securityService.hasExamAccess(authentication, #examId) or "
            + "@securityService.isExamInstructorOrAssistant(authentication, #examId)")
    public ExamResponse getExam(String examId) {
        try {
            log.debug("Getting exam with ID: {}", examId);
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            // Use two-step fetch to avoid Cartesian product issues
            Exam exam = examRepository.findById(examId)
                    .orElseThrow(() -> new ResourceNotFoundException("الامتحان غير موجود"));

            // Force load questions and answers
            exam.getQuestions().size(); // This triggers lazy loading
            for (ExamQuestion question : exam.getQuestions()) {
                question.getAnswers().size(); // This triggers lazy loading of answers
            }

            log.debug("Found exam: {} with {} questions", exam.getTitle(), exam.getQuestions().size());

            // Debug: Log each question and its answers
            for (ExamQuestion question : exam.getQuestions()) {
                log.debug("Question {}: {} (Type: {}, Points: {}) - {} answers",
                        question.getId(), question.getQuestionText(), question.getQuestionType(),
                        question.getPoints(), question.getAnswers().size());

                for (ExamAnswer answer : question.getAnswers()) {
                    log.debug("  Answer {}: {} (Correct: {})",
                            answer.getId(), answer.getAnswerText(), answer.isCorrect());
                }
            }

            ExamResponse response = mapExamToResponse(exam);
            log.debug("Mapped response has {} questions", response.getQuestions().size());

            // For students, hide correct answers and limit access
            if (!securityService.isAdmin(authentication)
                    && !securityService.isLessonInstructorOrAssistant(authentication, exam.getLesson().getId())) {

                // Verify student has access to this exam
                if (!studentLessonRepository.existsByStudentIdAndLessonId(
                        ((UserDetailsImpl) authentication.getPrincipal()).getId(), exam.getLesson().getId())) {
                    throw new LessonNotPurchasedException("يجب عليك شراء هذا الدرس أولاً للوصول إلى الامتحان");
                }

                // Hide correct answers and other sensitive info
                response.getQuestions().forEach(q -> {
                    q.setPoints(null); // Hide points from students before taking exam
                    q.getAnswers().forEach(a -> a.setCorrect(false));
                });
            }

            return response;
        } catch (ResourceNotFoundException e) {
            throw new ResourceNotFoundException("الامتحان غير موجود: " + examId);
        } catch (Exception e) {
            throw new ServiceException("فشل في الحصول على الامتحان",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ExamResultResponse getExamResults(String examId) {
        try {
            Exam exam = examRepository.findById(examId)
                    .orElseThrow(() -> new ResourceNotFoundException("Exam not found"));

            List<ExamSubmission> submissions = studentExamRepository.findByExamId(examId);

            if (submissions.isEmpty()) {
                return ExamResultResponse.builder()
                        .examId(examId)
                        .title(exam.getTitle())
                        .message("No submissions found for this exam")
                        .build();
            }

            long totalSubmissions = submissions.size();
            long passedCount = submissions.stream().filter(ExamSubmission::isPassed).count();
            BigDecimal passRate = new BigDecimal(passedCount)
                    .divide(new BigDecimal(totalSubmissions), 2, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));

            BigDecimal averageScore = submissions.stream()
                    .map(ExamSubmission::getTotalScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(totalSubmissions), 2, RoundingMode.HALF_UP);

            List<ExamResultResponse.QuestionStats> questionStats = exam.getQuestions().stream()
                    .map(question -> {
                        List<StudentExamAnswer> answers = studentExamAnswerRepository
                                .findByQuestionId(question.getId());

                        long correctCount = answers.stream()
                                .filter(StudentExamAnswer::isCorrect)
                                .count();

                        BigDecimal questionAvg = answers.stream()
                                .map(StudentExamAnswer::getPointsEarned)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                .divide(answers.isEmpty() ? BigDecimal.ONE : new BigDecimal(answers.size()),
                                        2, RoundingMode.HALF_UP);

                        return ExamResultResponse.QuestionStats.builder()
                                .questionId(question.getId())
                                .questionText(question.getQuestionText())
                                .averageScore(questionAvg)
                                .correctCount(correctCount)
                                .totalAttempts(answers.size())
                                .build();
                    })
                    .collect(Collectors.toList());

            return ExamResultResponse.builder()
                    .examId(examId)
                    .title(exam.getTitle())
                    .totalSubmissions(totalSubmissions)
                    .passedCount(passedCount)
                    .passRate(passRate)
                    .averageScore(averageScore)
                    .questionStats(questionStats)
                    .build();
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("Failed to get exam results: " + e.getMessage(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('STUDENT') and @securityService.hasExamAccess(authentication, #examId)")
    public ExamResponse submitExam(String examId, Map<String, String> answers, String studentId) {
        try {
            // Use two-step fetch to ensure all questions and answers are loaded
            Exam exam = examRepository.findById(examId)
                    .orElseThrow(() -> new ResourceNotFoundException("الامتحان غير موجود"));

            // Force load questions and answers
            exam.getQuestions().size(); // This triggers lazy loading
            for (ExamQuestion question : exam.getQuestions()) {
                question.getAnswers().size(); // This triggers lazy loading of answers
            }

            User student = userRepository.findById(studentId)
                    .orElseThrow(() -> new ResourceNotFoundException("الطالب غير موجود"));

            // Validate prerequisite
            progressService.validateLessonAccess(
                    studentId,
                    exam.getLesson().getId(),
                    LessonProgressServiceImpl.LessonPart.EXAM);

            // Check if already submitted
            if (studentExamRepository.existsByStudentIdAndExamId(studentId, examId)) {
                throw new BadRequestException("لقد قمت بتقديم هذا الامتحان بالفعل");
            }

            ExamSubmission submission = new ExamSubmission();
            submission.setExam(exam);
            submission.setStudent(student);
            submission.setSubmissionTime(LocalDateTime.now());

            BigDecimal totalScore = BigDecimal.ZERO;
            Map<String, QuestionResult> questionResults = new HashMap<>();

            for (ExamQuestion question : exam.getQuestions()) {
                String submittedAnswer = answers.get(question.getId());
                QuestionResult result = evaluateQuestion(question, submittedAnswer);

                totalScore = totalScore.add(result.getPointsEarned());
                questionResults.put(question.getId(), result);

                // Save each answer
                StudentExamAnswer studentAnswer = new StudentExamAnswer();
                studentAnswer.setSubmission(submission);
                studentAnswer.setQuestion(question);
                studentAnswer.setAnswerText(submittedAnswer);
                studentAnswer.setCorrect(result.isCorrect());
                studentAnswer.setPointsEarned(result.getPointsEarned());
                studentExamAnswerRepository.save(studentAnswer);
            }

            BigDecimal percentage = totalScore.divide(exam.getMaxPoints(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);

            boolean passed = percentage.compareTo(exam.getPassingScore()) >= 0;

            submission.setTotalScore(percentage);
            submission.setPassed(passed);
            studentExamRepository.save(submission);

            if (passed) {
                progressService.updateProgress(studentId, exam.getLesson().getId(),
                        LessonProgressStatus.EXAM_PASSED);
            }

            return buildExamResponse(exam, percentage, passed, questionResults);
        } catch (ResourceNotFoundException e) {
            throw new ResourceNotFoundException(e.getMessage());
        } catch (LessonAccessException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new ServiceException("فشل في تقديم الامتحان: " + e.getMessage(),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private QuestionResult evaluateQuestion(ExamQuestion question, String submittedAnswer) {
        if (submittedAnswer == null || submittedAnswer.isEmpty()) {
            return new QuestionResult(
                    question.getPoints(),
                    BigDecimal.ZERO,
                    false,
                    "لم يتم تقديم إجابة",
                    null);
        }

        switch (question.getQuestionType()) {
            case SINGLE_CHOICE:
                return evaluateSingleChoice(question, submittedAnswer);
            case MULTIPLE_CHOICE:
                return evaluateMultipleChoice(question, submittedAnswer);
            case TRUE_FALSE:
                return evaluateTrueFalse(question, submittedAnswer);
            default:
                return new QuestionResult(
                        question.getPoints(),
                        BigDecimal.ZERO,
                        false,
                        "نوع السؤال غير معروف",
                        submittedAnswer);
        }
    }

    private QuestionResult evaluateSingleChoice(ExamQuestion question, String submittedAnswer) {
        // For single choice, find the submitted answer and check if it's correct
        ExamAnswer submittedAnswerObj = question.getAnswers().stream()
                .filter(a -> a.getId().equals(submittedAnswer))
                .findFirst()
                .orElse(null);

        if (submittedAnswerObj == null) {
            return new QuestionResult(
                    question.getPoints(),
                    BigDecimal.ZERO,
                    false,
                    "إجابة غير صحيحة",
                    submittedAnswer);
        }

        boolean isCorrect = submittedAnswerObj.isCorrect();
        return new QuestionResult(
                question.getPoints(),
                isCorrect ? question.getPoints() : BigDecimal.ZERO,
                isCorrect,
                isCorrect ? "إجابة صحيحة" : "إجابة خاطئة",
                submittedAnswer);
    }

    private QuestionResult evaluateMultipleChoice(ExamQuestion question, String submittedAnswer) {
        // For multiple choice, submittedAnswer should be comma-separated answer IDs
        String[] submittedAnswerIds = submittedAnswer.split(",");
        Set<String> submittedSet = new HashSet<>(Arrays.asList(submittedAnswerIds));

        // Get all correct answer IDs
        Set<String> correctAnswerIds = question.getAnswers().stream()
                .filter(ExamAnswer::isCorrect)
                .map(ExamAnswer::getId)
                .collect(Collectors.toSet());

        // Check if submitted answers exactly match correct answers
        boolean isCorrect = submittedSet.equals(correctAnswerIds);

        return new QuestionResult(
                question.getPoints(),
                isCorrect ? question.getPoints() : BigDecimal.ZERO,
                isCorrect,
                isCorrect ? "إجابة صحيحة" : "إجابة خاطئة",
                submittedAnswer);
    }

    private QuestionResult evaluateTrueFalse(ExamQuestion question, String submittedAnswer) {
        // For TRUE_FALSE, submittedAnswer should be "true" or "false"
        ExamAnswer correctAnswer = question.getAnswers().stream()
                .filter(ExamAnswer::isCorrect)
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("السؤال لا يحتوي على إجابة صحيحة"));

        boolean isCorrect = correctAnswer.getAnswerText().equalsIgnoreCase(submittedAnswer);

        return new QuestionResult(
                question.getPoints(),
                isCorrect ? question.getPoints() : BigDecimal.ZERO,
                isCorrect,
                isCorrect ? "إجابة صحيحة" : "إجابة خاطئة",
                submittedAnswer);
    }

    private ExamResponse buildExamResponse(Exam exam, BigDecimal score,
            boolean passed, Map<String, QuestionResult> questionResults) {
        ExamResponse response = mapExamToResponse(exam);
        response.setScore(score);
        response.setPassed(passed);
        response.setQuestionResults(questionResults);
        response.setMaxPoints(exam.getMaxPoints());
        return response;
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @examRepository.findById(#examId).orElseThrow().lesson.id)")
    public void deleteExam(String examId) {
        try {
            Exam exam = examRepository.findById(examId)
                    .orElseThrow(() -> new ResourceNotFoundException("Exam not found"));

            // Delete all related questions and answers first
            examQuestionRepository.deleteByExamId(examId);
            examRepository.delete(exam);
        } catch (ResourceNotFoundException e) {
            log.error("Exam not found: {}", examId);
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete exam", e);
            throw new ServiceException("Failed to delete exam", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @examRepository.findById(#request.examId).orElseThrow().lesson.id)")
    public ExamResponse updateExam(ExamUpdateRequest request) {
        try {
            Exam exam = examRepository.findById(request.getExamId())
                    .orElseThrow(() -> new ResourceNotFoundException("Exam not found"));

            if (request.getTitle() != null) {
                exam.setTitle(request.getTitle());
            }
            if (request.getPassingScore() != null) {
                exam.setPassingScore(request.getPassingScore());
            }

            if (request.getQuestions() != null) {
                // Validate questions before updating
                for (ExamQuestionUpdateRequest questionReq : request.getQuestions()) {
                    if (questionReq.getQuestionType() != null && questionReq.getAnswers() != null) {
                        validateQuestionUpdate(questionReq);
                    }
                }
                updateExamQuestions(exam, request.getQuestions());
            }

            exam = examRepository.save(exam);
            return mapExamToResponse(exam);
        } catch (ResourceNotFoundException e) {
            log.error("Exam not found: {}", request.getExamId());
            throw e;
        } catch (Exception e) {
            log.error("Failed to update exam", e);
            throw new ServiceException("Failed to update exam", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private List<ExamAnswer> createAndSaveAnswersForQuestion(ExamQuestion question, ExamQuestionRequest questionReq) {
        List<ExamAnswer> answers = new ArrayList<>();
        int answerOrder = 1;

        for (ExamAnswerRequest answerReq : questionReq.getAnswers()) {
            ExamAnswer answer = ExamAnswer.builder()
                    .question(question)
                    .answerText(answerReq.getAnswerText())
                    .isCorrect(answerReq.isCorrect())
                    .answerOrder(answerOrder++)
                    .build();

            answer = examAnswerRepository.save(answer);
            log.debug("Answer saved with ID: {} (Text: {}, Correct: {})",
                    answer.getId(), answer.getAnswerText(), answer.isCorrect());
            answers.add(answer);
        }
        return answers;
    }

    private List<ExamAnswer> createAnswersForQuestion(ExamQuestion question, ExamQuestionRequest questionReq) {
        List<ExamAnswer> answers = new ArrayList<>();
        int answerOrder = 1;

        for (ExamAnswerRequest answerReq : questionReq.getAnswers()) {
            ExamAnswer answer = ExamAnswer.builder()
                    .question(question)
                    .answerText(answerReq.getAnswerText())
                    .isCorrect(answerReq.isCorrect())
                    .answerOrder(answerOrder++)
                    .build();

            answers.add(answer);
        }
        return answers;
    }

    private void updateExamQuestions(Exam exam, List<ExamQuestionUpdateRequest> questionRequests) {
        Set<String> existingQuestionIds = exam.getQuestions().stream()
                .map(ExamQuestion::getId)
                .collect(Collectors.toSet());

        Set<String> updatedQuestionIds = new HashSet<>();

        for (ExamQuestionUpdateRequest questionReq : questionRequests) {
            ExamQuestion question;
            if (questionReq.getId() != null) {
                question = examQuestionRepository.findById(questionReq.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Question not found"));
                updatedQuestionIds.add(question.getId());
            } else {
                question = new ExamQuestion();
                question.setExam(exam);
                exam.getQuestions().add(question);
            }

            if (questionReq.getQuestionText() != null) {
                question.setQuestionText(questionReq.getQuestionText());
            }
            if (questionReq.getQuestionType() != null) {
                question.setQuestionType(questionReq.getQuestionType());
            }
            if (questionReq.getPoints() != null) {
                question.setPoints(questionReq.getPoints());
            }

            if (questionReq.getAnswers() != null) {
                updateQuestionAnswers(question, questionReq.getAnswers());
            }
        }

        existingQuestionIds.removeAll(updatedQuestionIds);
        if (!existingQuestionIds.isEmpty()) {
            examQuestionRepository.deleteAllById(existingQuestionIds);
            exam.getQuestions().removeIf(q -> existingQuestionIds.contains(q.getId()));
        }
    }

    private void updateQuestionAnswers(ExamQuestion question, List<ExamAnswerUpdateRequest> answerRequests) {
        Set<String> existingAnswerIds = question.getAnswers().stream()
                .map(ExamAnswer::getId)
                .collect(Collectors.toSet());

        Set<String> updatedAnswerIds = new HashSet<>();

        for (ExamAnswerUpdateRequest answerReq : answerRequests) {
            ExamAnswer answer;
            if (answerReq.getId() != null) {
                answer = examAnswerRepository.findById(answerReq.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Answer not found"));
                updatedAnswerIds.add(answer.getId());
            } else {
                answer = new ExamAnswer();
                answer.setQuestion(question);
                question.getAnswers().add(answer);
            }

            if (answerReq.getAnswerText() != null) {
                answer.setAnswerText(answerReq.getAnswerText());
            }
            answer.setCorrect(answerReq.isCorrect());
        }

        existingAnswerIds.removeAll(updatedAnswerIds);
        if (!existingAnswerIds.isEmpty()) {
            examAnswerRepository.deleteAllById(existingAnswerIds);
            question.getAnswers().removeIf(a -> existingAnswerIds.contains(a.getId()));
        }
    }

    private ExamResponse mapExamToResponse(Exam exam) {
        return ExamResponse.builder()
                .id(exam.getId())
                .lessonId(exam.getLesson().getId())
                .title(exam.getTitle())
                .passingScore(exam.getPassingScore())
                .questions(exam.getQuestions().stream()
                        .map(this::mapQuestionToResponse)
                        .collect(Collectors.toList()))
                .build();
    }

    private ExamQuestionResponse mapQuestionToResponse(ExamQuestion question) {
        return ExamQuestionResponse.builder()
                .id(question.getId())
                .questionText(question.getQuestionText())
                .questionType(question.getQuestionType())
                .points(question.getPoints())
                .answers(question.getAnswers().stream()
                        .map(this::mapAnswerToResponse)
                        .collect(Collectors.toList()))
                .build();
    }

    private ExamAnswerResponse mapAnswerToResponse(ExamAnswer answer) {
        return ExamAnswerResponse.builder()
                .id(answer.getId())
                .answerText(answer.getAnswerText())
                .isCorrect(answer.isCorrect())
                .build();
    }

}
