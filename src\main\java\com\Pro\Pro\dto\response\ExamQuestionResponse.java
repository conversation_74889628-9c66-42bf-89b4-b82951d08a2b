package com.Pro.Pro.dto.response;

import com.Pro.Pro.model.QuestionType;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Data
@Builder
public class ExamQuestionResponse {
    private String id;
    private String questionText;
    private QuestionType questionType;
    private BigDecimal points;
    private List<ExamAnswerResponse> answers;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getQuestionText() {
        return questionText;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public void setQuestionType(QuestionType questionType) {
        this.questionType = questionType;
    }

    public BigDecimal getPoints() {
        return points;
    }

    public void setPoints(BigDecimal points) {
        this.points = points;
    }

    public List<ExamAnswerResponse> getAnswers() {
        return answers;
    }

    public void setAnswers(List<ExamAnswerResponse> answers) {
        this.answers = answers;
    }
}