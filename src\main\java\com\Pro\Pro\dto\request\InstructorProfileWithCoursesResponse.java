package com.Pro.Pro.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstructorProfileWithCoursesResponse {

    private String id;
    private String fullname;
    private String bio;
    private String photoUrl;
    private List<SimpleCourseResponse> courses;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SimpleCourseResponse {
        private String id;
        private String name;
        private String description;
        private String photoUrl;
        private List<SimpleLessonResponse> lessons;
    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SimpleLessonResponse {
        private String id;
        private String name;
        private String description;
        private String photoUrl;
    }
}