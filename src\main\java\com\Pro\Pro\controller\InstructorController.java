package com.Pro.Pro.controller;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.AccessCode;
import com.Pro.Pro.security.CurrentUser;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.InstructorService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/instructors")
@RequiredArgsConstructor
public class InstructorController {
    private final InstructorService instructorService;

    @PostMapping("/{instructorId}/courses")
    public ResponseEntity<?> createCourse(
            @PathVariable String instructorId,
            @Valid @RequestBody CourseRequest request) {
        try {
            return ResponseEntity.ok(instructorService.createCourse(request, instructorId));
        } catch (ResourceNotFoundException e) {
            log.error("Instructor not found: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to create course", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to create course");
        }
    }

    @PostMapping("/courses/{courseId}/lessons")
    public ResponseEntity<?> createLesson(
            @PathVariable String courseId,
            @Valid @RequestBody LessonRequest request) {
        try {
            return ResponseEntity.ok(instructorService.createLesson(request, courseId));
        } catch (ResourceNotFoundException e) {
            log.error("Course not found: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to create lesson", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to create lesson");
        }
    }

    @PostMapping("/lessons/{lessonId}/generate-codes")
    public ResponseEntity<?> generateAccessCodes(
            @PathVariable String lessonId,
            @RequestParam int count,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            return ResponseEntity.ok(instructorService.generateAccessCodes(lessonId, count, currentUser.getId()));
        } catch (ResourceNotFoundException e) {
            log.error("Resource not found: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (AccessDeniedException e) {
            log.error("Access denied: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to generate access codes", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to generate access codes");
        }
    }

    @GetMapping("/{instructorId}/courses")
    public ResponseEntity<?> getInstructorCourses(@PathVariable String instructorId) {
        try {
            return ResponseEntity.ok(instructorService.getInstructorCourses(instructorId));
        } catch (Exception e) {
            log.error("Failed to get courses for instructor: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to retrieve courses");
        }
    }

    @GetMapping("/courses/{courseId}/lessons")
    public ResponseEntity<?> getCourseLessons(@PathVariable String courseId) {
        try {
            return ResponseEntity.ok(instructorService.getCourseLessons(courseId));
        } catch (Exception e) {
            log.error("Failed to get lessons for course: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to retrieve lessons");
        }
    }

    @GetMapping("/{instructorId}/student-count")
    public ResponseEntity<?> getStudentCount(@PathVariable String instructorId) {
        try {
            return ResponseEntity.ok(instructorService.getStudentCount(instructorId));
        } catch (Exception e) {
            log.error("Failed to get student count for instructor: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to retrieve student count");
        }
    }

    @PutMapping("/{instructorId}/courses/{courseId}")
    public ResponseEntity<?> updateOwnCourse(
            @PathVariable String instructorId,
            @PathVariable String courseId,
            @Valid @RequestBody CourseRequest request,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            return ResponseEntity.ok(
                    instructorService.updateOwnCourse(courseId, request, currentUser.getId())
            );
        } catch (ResourceNotFoundException e) {
            log.error("Course not found: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (AccessDeniedException e) {
            log.error("Access denied: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to update course: {}", courseId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to update course");
        }
    }

    @PutMapping("/{instructorId}/lessons/{lessonId}")
    public ResponseEntity<?> updateOwnLesson(
            @PathVariable String instructorId,
            @PathVariable String lessonId,
            @Valid @RequestBody LessonRequest request,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            return ResponseEntity.ok(
                    instructorService.updateOwnLesson(lessonId, request, currentUser.getId())
            );
        } catch (ResourceNotFoundException e) {
            log.error("Lesson not found: {}", lessonId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (AccessDeniedException e) {
            log.error("Access denied: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to update lesson: {}", lessonId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to update lesson");
        }
    }

    @PutMapping("/{instructorId}/profile")
    public ResponseEntity<?> updateOwnProfile(
            @PathVariable Long instructorId,
            @Valid @RequestBody UpdateProfileRequest request,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            return ResponseEntity.ok(
                    instructorService.updateOwnProfile(currentUser.getId(), request)
            );
        } catch (ResourceNotFoundException e) {
            log.error("Instructor not found: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to update profile: {}", instructorId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to update profile");
        }
    }
}