package com.Pro.Pro.repository;

import com.Pro.Pro.model.Role;
import com.Pro.Pro.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, String> {
    Optional<User> findByEmail(String email);
    Optional<User> findByNationalId(String nationalId);
    Boolean existsByEmail(String email);
    Boolean existsByUsername(String username);
    Boolean existsByNationalId(String nationalId);
    List<User> findByInstructorId(String instructorId);
    Optional<User> findByIdAndRole(String id, Role role);
    List<User> findByRole(Role role);
    boolean existsByIdAndRoleAndInstructorId(String id, Role role, String instructorId);
    List<User> findByUsernameContainingIgnoreCase(String usernamePart);

    @Query(value = """
        SELECT u.id, u.email, u.username, u.role, 
               COUNT(c.id) as course_count 
        FROM users u 
        LEFT JOIN courses c ON u.id = c.instructor_id 
        WHERE u.role = 'INSTRUCTOR' 
        GROUP BY u.id
        """, nativeQuery = true)
    List<Object[]> findInstructorSummaries();

    @Query("SELECT u FROM User u " +
            "WHERE u.role = :role " +
            "AND (LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
            "OR LOWER(u.username) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) " +
            "ORDER BY " +
            "CASE WHEN :sortBy = 'email' THEN u.email END, " +
            "CASE WHEN :sortBy = 'username' THEN u.username END, " +
            "CASE WHEN :sortBy = 'createdAt' THEN u.createdAt END")
    Page<User> searchUsersByRole(
            @Param("role") Role role,
            @Param("searchTerm") String searchTerm,
            @Param("sortBy") String sortBy,
            Pageable pageable);

    @Query("SELECT COUNT(u) FROM User u WHERE u.role = :role")
    long countByRole(@Param("role") Role role);
}