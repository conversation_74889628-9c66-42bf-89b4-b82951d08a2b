package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.security.SecurityService;
import com.Pro.Pro.service.InstructorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InstructorServiceImpl implements InstructorService {

    private final UserRepository userRepository;
    private final CourseRepository courseRepository;
    private final LessonRepository lessonRepository;
    private final StudentLessonRepository studentLessonRepository;
    private final AccessCodeRepository accessCodeRepository;
    private final SecurityService securityService;
    private final InstructorProfileRepository instructorProfileRepository;

    @Override
    @PreAuthorize("@securityService.isAssistantForInstructor(authentication, #instructorId) || #instructorId == authentication.principal.id")
    public CourseResponse createCourse(CourseRequest request, String instructorId) {
        try {
            User instructor = userRepository.findByIdAndRole(instructorId, Role.INSTRUCTOR)
                    .orElseThrow(() -> new ResourceNotFoundException("Instructor not found with ID: " + instructorId));

            Course course = Course.builder()
                    .instructor(instructor)
                    .name(request.getName())
                    .description(request.getDescription())
                    .photoUrl(request.getPhotoUrl())
                    .build();

            course = courseRepository.save(course);
            return mapCourseToResponse(course);
        } catch (ResourceNotFoundException e) {
            log.error("Create course failed - Instructor not found: {}", instructorId, e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to create course for instructor: {}", instructorId, e);
            throw new CustomException("Failed to create course", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.isCourseInstructorOrAssistant(authentication, #courseId)")
    public LessonResponse createLesson(LessonRequest request, String courseId) {
        try {
            Course course = courseRepository.findById(courseId)
                    .orElseThrow(() -> new ResourceNotFoundException("Course not found with ID: " + courseId));

            Lesson lesson = Lesson.builder()
                    .course(course)
                    .name(request.getName())
                    .description(request.getDescription())
                    .photoUrl(request.getPhotoUrl())
                    .price(request.getPrice())
                    .videoUrl(request.getVideoUrl())
                    .build();

            lesson = lessonRepository.save(lesson);
            return mapLessonToResponse(lesson);
        } catch (ResourceNotFoundException e) {
            log.error("Create lesson failed - Course not found: {}", courseId, e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to create lesson for course: {}", courseId, e);
            throw new CustomException("Failed to create lesson", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.isCourseInstructorOrAssistant(authentication, #courseId) && #instructorId == authentication.principal.id")
    @Transactional
    public CourseResponse updateOwnCourse(String courseId, CourseRequest request, String instructorId) {
        try {
            Course course = courseRepository.findById(courseId)
                    .orElseThrow(() -> new ResourceNotFoundException("Course not found with ID: " + courseId));

            if (!course.getInstructor().getId().equals(instructorId)) {
                throw new AccessDeniedException("You can only update your own courses");
            }

            Optional.ofNullable(request.getName()).ifPresent(course::setName);
            Optional.ofNullable(request.getDescription()).ifPresent(course::setDescription);
            Optional.ofNullable(request.getPhotoUrl()).ifPresent(course::setPhotoUrl);

            course = courseRepository.save(course);
            return mapCourseToResponse(course);
        } catch (ResourceNotFoundException | AccessDeniedException e) {
            log.error("Update course failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to update course: {}", courseId, e);
            throw new CustomException("Failed to update course", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.isLessonInstructorOrAssistant(authentication, #lessonId) && #instructorId == authentication.principal.id")
    @Transactional
    public LessonResponse updateOwnLesson(String lessonId, LessonRequest request, String instructorId) {
        try {
            Lesson lesson = lessonRepository.findById(lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("Lesson not found with ID: " + lessonId));

            if (!lesson.getCourse().getInstructor().getId().equals(instructorId)) {
                throw new AccessDeniedException("You can only update your own lessons");
            }

            Optional.ofNullable(request.getName()).ifPresent(lesson::setName);
            Optional.ofNullable(request.getDescription()).ifPresent(lesson::setDescription);
            Optional.ofNullable(request.getPhotoUrl()).ifPresent(lesson::setPhotoUrl);
            Optional.ofNullable(request.getPrice()).ifPresent(lesson::setPrice);
            Optional.ofNullable(request.getVideoUrl()).ifPresent(lesson::setVideoUrl);

            lesson = lessonRepository.save(lesson);
            return mapLessonToResponse(lesson);
        } catch (ResourceNotFoundException | AccessDeniedException e) {
            log.error("Update lesson failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to update lesson: {}", lessonId, e);
            throw new CustomException("Failed to update lesson", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("#instructorId == authentication.principal.id")
    @Transactional
    public UserResponse updateOwnProfile(String instructorId, UpdateProfileRequest request) {
        try {
            User instructor = userRepository.findByIdAndRole(instructorId, Role.INSTRUCTOR)
                    .orElseThrow(() -> new ResourceNotFoundException("Instructor not found with ID: " + instructorId));

            Optional.ofNullable(request.getPhoneNumber()).ifPresent(instructor::setPhoneNumber);
            Optional.ofNullable(request.getAvatarUrl()).ifPresent(instructor::setAvatarUrl);

            User finalInstructor = userRepository.save(instructor);
            InstructorProfile profile = instructorProfileRepository.findByUserId(instructorId)
                    .orElseGet(() -> {
                        InstructorProfile newProfile = InstructorProfile.builder()
                                .user(finalInstructor)
                                .build();
                        return instructorProfileRepository.save(newProfile);
                    });

            Optional.ofNullable(request.getBio()).ifPresent(profile::setBio);
            Optional.ofNullable(request.getPhotoUrl()).ifPresent(profile::setPhotoUrl);

            instructorProfileRepository.save(profile);

            return mapUserToResponse(instructor);
        } catch (ResourceNotFoundException e) {
            log.error("Update profile failed - Instructor not found: {}", instructorId, e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to update profile for instructor: {}", instructorId, e);
            throw new CustomException("Failed to update profile", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    @Transactional
    public List<AccessCode> generateAccessCodes(String lessonId, int count, String creatorId) {
        try {
            Lesson lesson = lessonRepository.findById(lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("Lesson not found with ID: " + lessonId));

            User creator = userRepository.findById(creatorId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + creatorId));

            if (!securityService.isLessonInstructorOrAssistant(creatorId, lessonId)) {
                throw new AccessDeniedException("Not authorized to generate codes for this lesson");
            }

            List<AccessCode> codes = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                AccessCode code = AccessCode.builder()
                        .code(generateRandomCode())
                        .lesson(lesson)
                        .creator(creator)
                        .build();
                codes.add(accessCodeRepository.save(code));
            }
            return codes;
        } catch (ResourceNotFoundException | AccessDeniedException e) {
            log.error("Generate access codes failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to generate access codes for lesson: {}", lessonId, e);
            throw new CustomException("Failed to generate access codes", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.isAssistantForInstructor(authentication, #instructorId) || #instructorId == authentication.principal.id")
    public List<CourseResponse> getInstructorCourses(String instructorId) {
        try {
            return courseRepository.findByInstructorId(instructorId).stream()
                    .map(this::mapCourseToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get courses for instructor: {}", instructorId, e);
            throw new CustomException("Failed to retrieve courses", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.isCourseInstructorOrAssistant(authentication, #courseId)")
    public List<LessonResponse> getCourseLessons(String courseId) {
        try {
            return lessonRepository.findByCourseId(courseId).stream()
                    .map(this::mapLessonToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get lessons for course: {}", courseId, e);
            throw new CustomException("Failed to retrieve lessons", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.isAssistantForInstructor(authentication, #instructorId) || #instructorId == authentication.principal.id")
    public Long getStudentCount(String instructorId) {
        try {
            return studentLessonRepository.countByLessonCourseInstructorId(instructorId);
        } catch (Exception e) {
            log.error("Failed to get student count for instructor: {}", instructorId, e);
            throw new CustomException("Failed to retrieve student count", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private String generateRandomCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(8);
        for (int i = 0; i < 8; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    private UserResponse mapUserToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .username(user.getUsername())
                .phoneNumber(user.getPhoneNumber())
                .parentPhoneNumber(user.getParentPhoneNumber())
                .dateOfBirth(user.getDateOfBirth())
                .nationalId(user.getNationalId())
                .government(user.getGovernment())
                .avatarUrl(user.getAvatarUrl())
                .role(user.getRole().name())
                .createdAt(user.getCreatedAt())
                .build();
    }

    private CourseResponse mapCourseToResponse(Course course) {
        return CourseResponse.builder()
                .id(course.getId())
                .name(course.getName())
                .description(course.getDescription())
                .photoUrl(course.getPhotoUrl())
                .instructorId(course.getInstructor().getId())
                .instructorName(course.getInstructor().getEmail())
                .createdAt(course.getCreatedAt())
                .lessonCount(course.getLessons() != null ? course.getLessons().size() : 0)
                .build();
    }

    private LessonResponse mapLessonToResponse(Lesson lesson) {
        return LessonResponse.builder()
                .id(lesson.getId())
                .courseId(lesson.getCourse().getId())
                .name(lesson.getName())
                .description(lesson.getDescription())
                .photoUrl(lesson.getPhotoUrl())
                .price(lesson.getPrice())
                .build();
    }
}