package com.Pro.Pro.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenRouterConfig {

    @Value("${openrouter.api.key}")
    private String apiKey;

    @Value("${openrouter.api.url}")
    private String apiUrl;

    @Value("${openrouter.api.referer}")
    private String referer;

    @Value("${openrouter.api.title}")
    private String title;

    @Value("${openrouter.api.model}")
    private String model;

    // Getters
    public String getApiKey() { return apiKey; }
    public String getApiUrl() { return apiUrl; }
    public String getReferer() { return referer; }
    public String getTitle() { return title; }
    public String getModel() { return model; }
}