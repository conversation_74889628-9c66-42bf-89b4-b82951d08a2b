package com.Pro.Pro.dto.request;

import com.Pro.Pro.model.QuestionType;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class ExamQuestionUpdateRequest {
    private String id; // null for new questions
    private String questionText;
    private QuestionType questionType;
    private BigDecimal points;
    private List<ExamAnswerUpdateRequest> answers;
    // getters/setters
}