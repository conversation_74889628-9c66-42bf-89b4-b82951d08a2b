package com.Pro.Pro.dto.request;

import com.Pro.Pro.model.QuestionType;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class ExamQuestionUpdateRequest {

    private String id; // null for new questions
    private String questionText;
    private QuestionType questionType;
    private BigDecimal points;
    private List<ExamAnswerUpdateRequest> answers;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getQuestionText() {
        return questionText;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public QuestionType getQuestionType() {
        return questionType;
    }

    public void setQuestionType(QuestionType questionType) {
        this.questionType = questionType;
    }

    public BigDecimal getPoints() {
        return points;
    }

    public void setPoints(BigDecimal points) {
        this.points = points;
    }

    public List<ExamAnswerUpdateRequest> getAnswers() {
        return answers;
    }

    public void setAnswers(List<ExamAnswerUpdateRequest> answers) {
        this.answers = answers;
    }
}
