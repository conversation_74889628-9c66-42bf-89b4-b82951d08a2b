package com.Pro.Pro.controller;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.ResourceNotFoundException;
import com.Pro.Pro.security.CurrentUser;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.AssignmentService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/assignments")
@RequiredArgsConstructor
public class AssignmentController {

    private final AssignmentService assignmentService;

    @PostMapping("/lessons/{lessonId}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    public ResponseEntity<AssignmentResponse> createAssignment(
            @PathVariable String lessonId,
            @Valid @RequestBody AssignmentRequest request) {
        try {
            return ResponseEntity.ok(assignmentService.createAssignment(request, lessonId));
        } catch (Exception e) {
            log.error("Error creating assignment", e);
            throw e;
        }
    }

    @GetMapping("/{assignmentId}")
    @PreAuthorize("@securityService.hasLessonAccess(authentication.principal.id, #assignmentId) or " +
            "@securityService.isLessonInstructorOrAssistant(authentication, #assignmentId)")
    public ResponseEntity<AssignmentResponse> getAssignment(@PathVariable String assignmentId) {
        try {
            return ResponseEntity.ok(assignmentService.getAssignment(assignmentId));
        } catch (Exception e) {
            log.error("Error getting assignment", e);
            throw e;
        }
    }

    @PostMapping("/{assignmentId}/submit")
    @PreAuthorize("hasRole('STUDENT')")
    public ResponseEntity<AssignmentResponse> submitAssignment(
            @PathVariable String assignmentId,
            @RequestBody String submissionText,
            @CurrentUser UserDetailsImpl currentUser) {
        try {
            return ResponseEntity.ok(assignmentService.submitAssignment(
                    assignmentId, submissionText, currentUser.getId()));
        } catch (Exception e) {
            log.error("Error submitting assignment", e);
            throw e;
        }
    }

    @PostMapping("/submissions/{submissionId}/grade")
    @PreAuthorize("@securityService.isAssignmentInstructorOrAssistant(authentication.principal.id, #submissionId)")
    public ResponseEntity<AssignmentResponse> gradeAssignment(
            @PathVariable String submissionId,
            @RequestParam BigDecimal grade,
            @RequestParam(required = false) String feedback) {
        try {
            return ResponseEntity.ok(assignmentService.gradeAssignment(submissionId, grade, feedback));
        } catch (Exception e) {
            log.error("Error grading assignment", e);
            throw e;
        }
    }

    @DeleteMapping("/{assignmentId}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @assignmentRepository.findById(#assignmentId).orElseThrow().lesson.id)")
    public ResponseEntity<Void> deleteAssignment(@PathVariable String assignmentId) {
        try {
            assignmentService.deleteAssignment(assignmentId);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("Error deleting assignment", e);
            throw e;
        }
    }

    @PutMapping
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @assignmentRepository.findById(#request.assignmentId).orElseThrow().lesson.id)")
    public ResponseEntity<AssignmentResponse> updateAssignment(
            @Valid @RequestBody AssignmentUpdateRequest request) {
        try {
            return ResponseEntity.ok(assignmentService.updateAssignment(request));
        } catch (Exception e) {
            log.error("Error updating assignment", e);
            throw e;
        }
    }

    @GetMapping("/instructors/{instructorId}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isInstructorOrAssistant(authentication, #instructorId)")
    public ResponseEntity<List<AssignmentResponse>> getAssignmentsByInstructor(@PathVariable String instructorId) {
        try {
            return ResponseEntity.ok(assignmentService.getAssignmentsByInstructor(instructorId));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error retrieving assignments for instructor: {}", instructorId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Page<AssignmentResponse>> getAllAssignments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            return ResponseEntity.ok(assignmentService.getAllAssignments(pageable));
        } catch (Exception e) {
            log.error("Error retrieving assignments", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/lessons/{lessonId}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    public ResponseEntity<List<AssignmentResponse>> getAssignmentsByLesson(@PathVariable String lessonId) {
        try {
            return ResponseEntity.ok(assignmentService.getAssignmentsByLesson(lessonId));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error retrieving assignments for lesson: {}", lessonId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{assignmentId}/submissions")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isAssignmentInstructorOrAssistant(authentication, #assignmentId)")
    public ResponseEntity<Page<AssignmentSubmissionResponse>> getAssignmentSubmissions(
            @PathVariable String assignmentId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            return ResponseEntity.ok(assignmentService.getAssignmentSubmissions(assignmentId, pageable));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error retrieving submissions for assignment: {}", assignmentId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}