package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;
import java.util.Date;


@Data
@Builder
public class UserResponse {

    private String id;
    private String email;
    private String username;
    private String fullname;
    private String phoneNumber;
    private String parentPhoneNumber;
    private Date dateOfBirth;
    private String nationalId;
    private String government;
    private String avatarUrl;
    private String role;
    private String instructorName;
    private Date createdAt;

}
