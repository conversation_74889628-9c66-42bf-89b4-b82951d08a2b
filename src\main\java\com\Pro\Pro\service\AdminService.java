package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.AccessCodeResponse;
import com.Pro.Pro.dto.response.CourseResponse;
import com.Pro.Pro.dto.response.LessonResponse;
import com.Pro.Pro.dto.response.UserResponse;
import com.Pro.Pro.model.AccessCode;
import com.Pro.Pro.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;


public interface AdminService {
    UserResponse createInstructor(CreateInstructorRequest request);
    UserResponse createAssistant(CreateAssistantRequest request, String instructorId);
    CourseResponse createCourse(CourseRequest request, String instructorId);
    LessonResponse createLesson(LessonRequest request, String courseId);
    List<UserResponse> getAllStudents();
    List<AccessCode> generateAccessCodes(String lessonId, int count , String adminId);
    void deleteUser(String userId);
    void deleteCourse(String courseId);
    void deleteLesson(String lessonId);
    CourseResponse updateCourse(String courseId, CourseRequest request);
    LessonResponse updateLesson(String lessonId, LessonRequest request);
    UserResponse updateInstructorProfile(String instructorId, UpdateProfileRequest request);
    List<User> searchStudentsByUsername(String usernamePart);
    Page<AccessCodeResponse> getAllAccessCodes(Pageable pageable);
}