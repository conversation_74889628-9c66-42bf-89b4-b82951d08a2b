package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;


public interface AssignmentService {
    AssignmentResponse createAssignment(AssignmentRequest request, String lessonId);
    AssignmentResponse getAssignment(String assignmentId);
    AssignmentResponse submitAssignment(String assignmentId, String submissionText, String studentId);
    AssignmentResponse gradeAssignment(String submissionId, BigDecimal grade, String feedback);
    void deleteAssignment(String assignmentId);
    AssignmentResponse updateAssignment(AssignmentUpdateRequest request);
    Page<AssignmentResponse> getAllAssignments(Pageable pageable);
    List<AssignmentResponse> getAssignmentsByLesson(String lessonId);
    Page<AssignmentSubmissionResponse> getAssignmentSubmissions(String assignmentId, Pageable pageable);
    List<AssignmentResponse> getAssignmentsByInstructor(String instructorId);
}