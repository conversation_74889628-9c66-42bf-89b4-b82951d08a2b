package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import java.math.BigDecimal;


public interface AssignmentService {
    AssignmentResponse createAssignment(AssignmentRequest request, String lessonId);
    AssignmentResponse getAssignment(String assignmentId);
    AssignmentResponse submitAssignment(String assignmentId, String submissionText, String studentId);
    AssignmentResponse gradeAssignment(String submissionId, BigDecimal grade, String feedback);
    void deleteAssignment(String assignmentId);
    AssignmentResponse updateAssignment(AssignmentUpdateRequest request);
}