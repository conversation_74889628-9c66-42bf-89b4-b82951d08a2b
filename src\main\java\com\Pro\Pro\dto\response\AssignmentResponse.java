package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
public class AssignmentResponse {
    private String id;
    private String lessonId;
    private String title;
    private String description;
    private Date dueDate;
    private BigDecimal maxPoints;
    private String assignmentId;
    private String studentId;
    private String submissionText;
    private Date submissionDate;
    private BigDecimal grade;
    private String feedback;
    private List<AssignmentSubmissionResponse> submissions;
}