package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Builder
public class AssignmentResponse {

    // Assignment fields
    private String id;
    private String lessonId;
    private String title;
    private String description;
    private Date dueDate;
    private BigDecimal maxPoints;

    // Student submission fields (when used for submission response)
    private String assignmentId;
    private String studentId;
    private String submissionText;
    private Date submissionDate;
    private BigDecimal grade;
    private String feedback;

    // Additional information fields
    private Boolean isOverdue;
    private String timeRemaining;
    private Integer submissionCount;
    private BigDecimal averageGrade;
    private Integer gradedCount;

    // Related data
    private List<AssignmentSubmissionResponse> submissions;
}
