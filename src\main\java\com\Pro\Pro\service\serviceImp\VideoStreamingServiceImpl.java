//package com.Pro.Pro.service.serviceImp;
//import org.springframework.core.io.Resource; // Instead of jakarta.annotation.Resource
//import java.nio.file.Path;
//import com.Pro.Pro.model.Role;
//import com.Pro.Pro.model.User;
//import com.Pro.Pro.repository.LessonRepository;
//import com.Pro.Pro.repository.StudentLessonRepository;
//import com.Pro.Pro.repository.UserRepository;
//import com.Pro.Pro.service.VideoStreamingService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.core.io.ResourceLoader;
//import org.springframework.data.rest.webmvc.ResourceNotFoundException;
//import org.springframework.security.access.AccessDeniedException;
//import org.springframework.stereotype.Service;
//
//import java.nio.file.Paths;
//
//@Service
//@RequiredArgsConstructor
//public class VideoStreamingServiceImpl implements VideoStreamingService {
//    private final LessonRepository lessonRepository;
//    private final UserRepository userRepository;
//    private final StudentLessonRepository studentLessonRepository;
//    private final ResourceLoader resourceLoader;
//
//    @Value("${video.storage.location}")
//    private String videoStorageLocation;
//
//    @Override
//    public org.springframework.core.io.Resource loadVideo(String videoUrl, Long lessonId, Long userId)
//    {
//        if (!hasVideoAccess(lessonId, userId)) {
//            throw new AccessDeniedException("You don't have access to this video");
//        }
//
//        try {
//            Path videoPath = Paths.get(videoStorageLocation).resolve(videoUrl).normalize();
//            Resource resource = resourceLoader.getResource("file:" + videoPath.toString());
//
//            if (resource.exists() && resource.isReadable()) {
//                return resource;
//            } else {
//                throw new ResourceNotFoundException("Video file not found");
//            }
//        } catch (Exception e) {
//            throw new ResourceNotFoundException("Could not load the video file", e);
//        }
//    }
//
//    @Override
//    public boolean hasVideoAccess(Long lessonId, Long userId) {
//        User user = userRepository.findById(userId)
//                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
//
//        if (user.getRole() == Role.ADMIN) {
//            return true;
//        }
//
//        // Check if user is instructor/assistant for this lesson
//        if (user.getRole() == Role.INSTRUCTOR || user.getRole() == Role.ASSISTANT) {
//            return lessonRepository.existsByIdAndCourseInstructorId(lessonId, userId) ||
//                    (user.getInstructor() != null &&
//                            lessonRepository.existsByIdAndCourseInstructorId(lessonId, user.getInstructor().getId()));
//        }
//
//        // For students
//        return studentLessonRepository.existsByStudentIdAndLessonId(userId, lessonId);
//    }
//}