package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.AssignmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssignmentServiceImpl implements AssignmentService {

    private final AssignmentRepository assignmentRepository;
    private final LessonRepository lessonRepository;
    private final StudentAssignmentRepository studentAssignmentRepository;
    private final UserRepository userRepository;
    private final LessonProgressServiceImpl progressService;

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    public AssignmentResponse createAssignment(AssignmentRequest request, String lessonId) {
        log.info("إنشاء واجب جديد للدرس: {}", lessonId);

        try {
            // التحقق من صحة البيانات المدخلة
            validateAssignmentRequest(request);

            // البحث عن الدرس
            Lesson lesson = lessonRepository.findById(lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("الدرس غير موجود بالمعرف: " + lessonId));

            // التحقق من عدم وجود واجب آخر بنفس العنوان في نفس الدرس
            if (assignmentRepository.existsByLessonIdAndTitle(lessonId, request.getTitle().trim())) {
                throw new BadRequestException("يوجد واجب آخر بنفس العنوان في هذا الدرس");
            }

            // إنشاء الواجب
            Assignment assignment = Assignment.builder()
                    .lesson(lesson)
                    .title(request.getTitle().trim())
                    .description(request.getDescription() != null ? request.getDescription().trim() : null)
                    .dueDate(request.getDueDate())
                    .maxPoints(request.getMaxPoints().setScale(2, RoundingMode.HALF_UP))
                    .build();

            assignment = assignmentRepository.save(assignment);
            log.info("تم إنشاء الواجب بنجاح بالمعرف: {}", assignment.getId());

            return mapAssignmentToResponse(assignment);

        } catch (ResourceNotFoundException e) {
            log.error("الدرس غير موجود: {}", lessonId);
            throw e;
        } catch (BadRequestException e) {
            log.warn("خطأ في بيانات إنشاء الواجب: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("فشل في إنشاء الواجب", e);
            throw new ServiceException("فشل في إنشاء الواجب: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.hasLessonAccess(authentication.principal.id, @assignmentRepository.findById(#assignmentId).orElseThrow().lesson.id) or " +
            "@securityService.isLessonInstructorOrAssistant(authentication, @assignmentRepository.findById(#assignmentId).orElseThrow().lesson.id)")
    public AssignmentResponse getAssignment(String assignmentId) {
        log.info("استرجاع الواجب بالمعرف: {}", assignmentId);

        try {
            // التحقق من صحة المعرف
            if (!StringUtils.hasText(assignmentId)) {
                throw new BadRequestException("معرف الواجب مطلوب");
            }

            Assignment assignment = assignmentRepository.findById(assignmentId)
                    .orElseThrow(() -> new ResourceNotFoundException("الواجب غير موجود بالمعرف: " + assignmentId));

            AssignmentResponse response = mapAssignmentToResponse(assignment);

            // إضافة معلومات إضافية للواجب
            response.setIsOverdue(isAssignmentOverdue(assignment));
            response.setTimeRemaining(calculateTimeRemaining(assignment));
            response.setSubmissionCount(getSubmissionCount(assignmentId));

            // للمدرسين والإداريين، إضافة تفاصيل التسليمات
            if (studentAssignmentRepository.existsByAssignmentId(assignmentId)) {
                List<StudentAssignment> submissions = studentAssignmentRepository.findByAssignmentId(assignmentId);
                response.setSubmissions(submissions.stream()
                        .map(this::mapStudentAssignmentToSubmissionResponse)
                        .collect(Collectors.toList()));

                // إضافة إحصائيات
                response.setAverageGrade(calculateAverageGrade(submissions));
                response.setGradedCount(countGradedSubmissions(submissions));
            }

            log.info("تم استرجاع الواجب بنجاح: {}", assignmentId);
            return response;

        } catch (ResourceNotFoundException e) {
            log.error("الواجب غير موجود: {}", assignmentId);
            throw e;
        } catch (BadRequestException e) {
            log.warn("خطأ في بيانات استرجاع الواجب: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("فشل في استرجاع الواجب", e);
            throw new ServiceException("فشل في استرجاع الواجب: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('STUDENT') and @securityService.hasLessonAccess(authentication.principal.id, @assignmentRepository.findById(#assignmentId).orElseThrow().lesson.id)")
    public AssignmentResponse submitAssignment(String assignmentId, String submissionText, String studentId) {
        log.info("تسليم الواجب - الطالب: {}, الواجب: {}", studentId, assignmentId);

        try {
            // التحقق من صحة البيانات المدخلة
            validateSubmissionData(assignmentId, submissionText, studentId);

            Assignment assignment = assignmentRepository.findById(assignmentId)
                    .orElseThrow(() -> new ResourceNotFoundException("الواجب غير موجود بالمعرف: " + assignmentId));

            User student = userRepository.findById(studentId)
                    .orElseThrow(() -> new ResourceNotFoundException("الطالب غير موجود بالمعرف: " + studentId));

            // التحقق من انتهاء موعد التسليم
            if (isAssignmentOverdue(assignment)) {
                throw new BadRequestException("انتهى موعد تسليم الواجب في: " + assignment.getDueDate());
            }

            // التحقق من المتطلبات المسبقة - يجب على الطالب مشاهدة الفيديو أولاً
            try {
                progressService.validateLessonAccess(
                        studentId,
                        assignment.getLesson().getId(),
                        LessonProgressServiceImpl.LessonPart.VIDEO);
            } catch (LessonAccessException e) {
                throw new BadRequestException("يجب مشاهدة فيديو الدرس قبل تسليم الواجب");
            }

            // البحث عن تسليم سابق أو إنشاء جديد
            StudentAssignment studentAssignment = studentAssignmentRepository
                    .findByStudentIdAndAssignmentId(studentId, assignmentId)
                    .orElseGet(() -> StudentAssignment.builder()
                    .student(student)
                    .assignment(assignment)
                    .build());

            // التحقق من وجود تسليم سابق مُقيم
            if (studentAssignment.getGrade() != null) {
                log.warn("محاولة إعادة تسليم واجب مُقيم - الطالب: {}, الواجب: {}", studentId, assignmentId);
                throw new BadRequestException("لا يمكن إعادة تسليم واجب تم تقييمه بالفعل");
            }

            // حفظ التسليم
            studentAssignment.setSubmissionText(submissionText.trim());
            studentAssignment.setSubmissionDate(new Date());
            studentAssignment = studentAssignmentRepository.save(studentAssignment);

            // تحديث التقدم
            progressService.updateProgress(
                    studentId,
                    assignment.getLesson().getId(),
                    LessonProgressStatus.ASSIGNMENT_DONE);

            log.info("تم تسليم الواجب بنجاح - الطالب: {}, الواجب: {}", studentId, assignmentId);
            return mapStudentAssignmentToResponse(studentAssignment);

        } catch (ResourceNotFoundException e) {
            log.error("مورد غير موجود: {}", e.getMessage());
            throw e;
        } catch (BadRequestException e) {
            log.warn("خطأ في تسليم الواجب: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("فشل في تسليم الواجب", e);
            throw new ServiceException("فشل في تسليم الواجب: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("@securityService.isAssignmentInstructorOrAssistant(authentication.principal.id, #submissionId)")
    public AssignmentResponse gradeAssignment(String submissionId, BigDecimal grade, String feedback) {
        try {
            StudentAssignment submission = studentAssignmentRepository.findById(submissionId)
                    .orElseThrow(() -> new ResourceNotFoundException("Submission not found"));

            if (grade.compareTo(submission.getAssignment().getMaxPoints()) > 0) {
                throw new BadRequestException("Grade cannot exceed max points");
            }

            submission.setGrade(grade);
            submission.setFeedback(feedback);
            submission = studentAssignmentRepository.save(submission);

            return mapStudentAssignmentToResponse(submission);
        } catch (ResourceNotFoundException e) {
            log.error("Submission not found: {}", submissionId);
            throw e;
        } catch (BadRequestException e) {
            log.warn("Invalid grade: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Failed to grade assignment", e);
            throw new ServiceException("Failed to grade assignment: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @assignmentRepository.findById(#assignmentId).orElseThrow().lesson.id)")
    public void deleteAssignment(String assignmentId) {
        try {
            Assignment assignment = assignmentRepository.findById(assignmentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Assignment not found"));

            // Delete all related submissions first
            studentAssignmentRepository.deleteByAssignmentId(assignmentId);
            assignmentRepository.delete(assignment);
        } catch (ResourceNotFoundException e) {
            log.error("Assignment not found: {}", assignmentId);
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete assignment", e);
            throw new ServiceException("Failed to delete assignment", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @assignmentRepository.findById(#request.assignmentId).orElseThrow().lesson.id)")
    public AssignmentResponse updateAssignment(AssignmentUpdateRequest request) {
        try {
            Assignment assignment = assignmentRepository.findById(request.getAssignmentId())
                    .orElseThrow(() -> new ResourceNotFoundException("Assignment not found"));

            if (request.getTitle() != null) {
                assignment.setTitle(request.getTitle());
            }
            if (request.getDescription() != null) {
                assignment.setDescription(request.getDescription());
            }
            if (request.getDueDate() != null) {
                assignment.setDueDate(request.getDueDate());
            }
            if (request.getMaxPoints() != null) {
                assignment.setMaxPoints(request.getMaxPoints());
            }

            assignment = assignmentRepository.save(assignment);
            return mapAssignmentToResponse(assignment);
        } catch (ResourceNotFoundException e) {
            log.error("Assignment not found: {}", request.getAssignmentId());
            throw e;
        } catch (Exception e) {
            log.error("Failed to update assignment", e);
            throw new ServiceException("Failed to update assignment", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private AssignmentResponse mapAssignmentToResponse(Assignment assignment) {
        return AssignmentResponse.builder()
                .id(assignment.getId())
                .lessonId(assignment.getLesson().getId())
                .title(assignment.getTitle())
                .description(assignment.getDescription())
                .dueDate(assignment.getDueDate())
                .maxPoints(assignment.getMaxPoints())
                .build();
    }

    private AssignmentResponse mapStudentAssignmentToResponse(StudentAssignment submission) {
        return AssignmentResponse.builder()
                .id(submission.getId())
                .assignmentId(submission.getAssignment().getId())
                .studentId(submission.getStudent().getId())
                .submissionText(submission.getSubmissionText())
                .submissionDate(submission.getSubmissionDate())
                .grade(submission.getGrade())
                .feedback(submission.getFeedback())
                .build();
    }

    private AssignmentSubmissionResponse mapStudentAssignmentToSubmissionResponse(StudentAssignment submission) {
        return AssignmentSubmissionResponse.builder()
                .submissionId(submission.getId())
                .studentId(submission.getStudent().getId())
                .studentName(submission.getStudent().getFullname())
                .submissionDate(submission.getSubmissionDate())
                .grade(submission.getGrade())
                .build();
    }

    // =============== Helper Methods ===============
    /**
     * التحقق من صحة بيانات طلب إنشاء الواجب
     */
    private void validateAssignmentRequest(AssignmentRequest request) {
        if (!StringUtils.hasText(request.getTitle())) {
            throw new BadRequestException("عنوان الواجب مطلوب");
        }

        if (request.getTitle().trim().length() < 3) {
            throw new BadRequestException("عنوان الواجب يجب أن يكون 3 أحرف على الأقل");
        }

        if (request.getTitle().trim().length() > 200) {
            throw new BadRequestException("عنوان الواجب يجب أن يكون أقل من 200 حرف");
        }

        if (request.getMaxPoints() == null || request.getMaxPoints().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BadRequestException("النقاط القصوى يجب أن تكون أكبر من صفر");
        }

        if (request.getMaxPoints().compareTo(new BigDecimal("1000")) > 0) {
            throw new BadRequestException("النقاط القصوى يجب أن تكون أقل من أو تساوي 1000");
        }

        if (request.getDueDate() != null && request.getDueDate().before(new Date())) {
            throw new BadRequestException("تاريخ التسليم يجب أن يكون في المستقبل");
        }

        if (request.getDescription() != null && request.getDescription().trim().length() > 2000) {
            throw new BadRequestException("وصف الواجب يجب أن يكون أقل من 2000 حرف");
        }
    }

    /**
     * التحقق من صحة بيانات تسليم الواجب
     */
    private void validateSubmissionData(String assignmentId, String submissionText, String studentId) {
        if (!StringUtils.hasText(assignmentId)) {
            throw new BadRequestException("معرف الواجب مطلوب");
        }

        if (!StringUtils.hasText(studentId)) {
            throw new BadRequestException("معرف الطالب مطلوب");
        }

        if (!StringUtils.hasText(submissionText)) {
            throw new BadRequestException("نص التسليم مطلوب");
        }

        if (submissionText.trim().length() < 10) {
            throw new BadRequestException("نص التسليم يجب أن يكون 10 أحرف على الأقل");
        }

        if (submissionText.trim().length() > 10000) {
            throw new BadRequestException("نص التسليم يجب أن يكون أقل من 10000 حرف");
        }
    }

    /**
     * التحقق من انتهاء موعد تسليم الواجب
     */
    private boolean isAssignmentOverdue(Assignment assignment) {
        if (assignment.getDueDate() == null) {
            return false; // لا يوجد موعد نهائي
        }
        return assignment.getDueDate().before(new Date());
    }

    /**
     * حساب الوقت المتبقي لتسليم الواجب
     */
    private String calculateTimeRemaining(Assignment assignment) {
        if (assignment.getDueDate() == null) {
            return "لا يوجد موعد نهائي";
        }

        Date now = new Date();
        if (assignment.getDueDate().before(now)) {
            return "انتهى الموعد";
        }

        long diffInMillis = assignment.getDueDate().getTime() - now.getTime();
        long days = diffInMillis / (24 * 60 * 60 * 1000);
        long hours = (diffInMillis % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);

        if (days > 0) {
            return days + " يوم و " + hours + " ساعة";
        } else if (hours > 0) {
            return hours + " ساعة";
        } else {
            long minutes = (diffInMillis % (60 * 60 * 1000)) / (60 * 1000);
            return minutes + " دقيقة";
        }
    }

    /**
     * حساب عدد التسليمات
     */
    private int getSubmissionCount(String assignmentId) {
        return studentAssignmentRepository.countByAssignmentId(assignmentId);
    }

    /**
     * حساب متوسط الدرجات
     */
    private BigDecimal calculateAverageGrade(List<StudentAssignment> submissions) {
        List<StudentAssignment> gradedSubmissions = submissions.stream()
                .filter(s -> s.getGrade() != null)
                .collect(Collectors.toList());

        if (gradedSubmissions.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal total = gradedSubmissions.stream()
                .map(StudentAssignment::getGrade)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return total.divide(new BigDecimal(gradedSubmissions.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * حساب عدد التسليمات المُقيمة
     */
    private int countGradedSubmissions(List<StudentAssignment> submissions) {
        return (int) submissions.stream()
                .filter(s -> s.getGrade() != null)
                .count();
    }

    // =============== Additional Service Methods ===============
    /**
     * الحصول على جميع واجبات الدرس
     */
    @PreAuthorize("@securityService.hasLessonAccess(authentication.principal.id, #lessonId) or "
            + "@securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    public List<AssignmentResponse> getAssignmentsByLesson(String lessonId) {
        log.info("استرجاع واجبات الدرس: {}", lessonId);

        try {
            if (!StringUtils.hasText(lessonId)) {
                throw new BadRequestException("معرف الدرس مطلوب");
            }

            if (!lessonRepository.existsById(lessonId)) {
                throw new ResourceNotFoundException("الدرس غير موجود بالمعرف: " + lessonId);
            }

            List<Assignment> assignments = assignmentRepository.findByLessonIdOrderByCreatedAtDesc(lessonId);

            return assignments.stream()
                    .map(this::mapAssignmentToResponse)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("فشل في استرجاع واجبات الدرس: {}", lessonId, e);
            throw new ServiceException("فشل في استرجاع واجبات الدرس", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * الحصول على تسليمات الطالب
     */
    @PreAuthorize("hasRole('STUDENT') and authentication.principal.id == #studentId")
    public List<AssignmentResponse> getStudentSubmissions(String studentId, Pageable pageable) {
        log.info("استرجاع تسليمات الطالب: {}", studentId);

        try {
            if (!StringUtils.hasText(studentId)) {
                throw new BadRequestException("معرف الطالب مطلوب");
            }

            if (!userRepository.existsById(studentId)) {
                throw new ResourceNotFoundException("الطالب غير موجود بالمعرف: " + studentId);
            }

            Page<StudentAssignment> submissions = studentAssignmentRepository
                    .findByStudentIdOrderBySubmissionDateDesc(studentId, pageable);

            return submissions.getContent().stream()
                    .map(this::mapStudentAssignmentToResponse)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("فشل في استرجاع تسليمات الطالب: {}", studentId, e);
            throw new ServiceException("فشل في استرجاع تسليمات الطالب", e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
