package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.AssignmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssignmentServiceImpl implements AssignmentService {

    private final AssignmentRepository assignmentRepository;
    private final LessonRepository lessonRepository;
    private final StudentAssignmentRepository studentAssignmentRepository;
    private final UserRepository userRepository;
    private final LessonProgressServiceImpl progressService;

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, #lessonId)")
    public AssignmentResponse createAssignment(AssignmentRequest request, String lessonId) {
        try {
            Lesson lesson = lessonRepository.findById(lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("Lesson not found with ID: " + lessonId));

            Assignment assignment = Assignment.builder()
                    .lesson(lesson)
                    .title(request.getTitle())
                    .description(request.getDescription())
                    .dueDate(request.getDueDate())
                    .maxPoints(request.getMaxPoints())
                    .build();

            assignment = assignmentRepository.save(assignment);
            return mapAssignmentToResponse(assignment);
        } catch (ResourceNotFoundException e) {
            log.error("Lesson not found: {}", lessonId);
            throw e;
        } catch (Exception e) {
            log.error("Failed to create assignment", e);
            throw new ServiceException("Failed to create assignment: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("@securityService.hasLessonAccess(authentication.principal.id, #assignmentId) or " +
            "@securityService.isLessonInstructorOrAssistant(authentication, #assignmentId)")
    public AssignmentResponse getAssignment(String assignmentId) {
        try {
            Assignment assignment = assignmentRepository.findById(assignmentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Assignment not found with ID: " + assignmentId));

            AssignmentResponse response = mapAssignmentToResponse(assignment);

            // For instructors/admins, include student submissions
            if (studentAssignmentRepository.existsByAssignmentId(assignmentId)) {
                List<StudentAssignment> submissions = studentAssignmentRepository.findByAssignmentId(assignmentId);
                response.setSubmissions(submissions.stream()
                        .map(this::mapStudentAssignmentToSubmissionResponse)
                        .toList());
            }

            return response;
        } catch (ResourceNotFoundException e) {
            log.error("Assignment not found: {}", assignmentId);
            throw e;
        } catch (Exception e) {
            log.error("Failed to get assignment", e);
            throw new ServiceException("Failed to get assignment: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('STUDENT') and @securityService.hasLessonAccess(authentication.principal.id, @assignmentRepository.findById(#assignmentId).orElseThrow().lesson.id)")
    public AssignmentResponse submitAssignment(String assignmentId, String submissionText, String studentId) {
        try {
            Assignment assignment = assignmentRepository.findById(assignmentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Assignment not found"));

            User student = userRepository.findById(studentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Student not found"));

            // Validate prerequisite - student must have watched the video first
            progressService.validateLessonAccess(
                    studentId,
                    assignment.getLesson().getId(),
                    LessonProgressServiceImpl.LessonPart.VIDEO);

            StudentAssignment studentAssignment = studentAssignmentRepository
                    .findByStudentIdAndAssignmentId(studentId, assignmentId)
                    .orElseGet(() -> StudentAssignment.builder()
                            .student(student)
                            .assignment(assignment)
                            .build());

            studentAssignment.setSubmissionText(submissionText);
            studentAssignment.setSubmissionDate(new Date());
            studentAssignment = studentAssignmentRepository.save(studentAssignment);

            // Update progress
            progressService.updateProgress(
                    studentId,
                    assignment.getLesson().getId(),
                    LessonProgressStatus.ASSIGNMENT_DONE);

            return mapStudentAssignmentToResponse(studentAssignment);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (LessonAccessException e) {
            log.warn("Assignment submission failed: {}", e.getMessage());
            throw new BadRequestException("Cannot submit assignment: " + e.getMessage());
        } catch (Exception e) {
            log.error("Failed to submit assignment", e);
            throw new ServiceException("Failed to submit assignment", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("@securityService.isAssignmentInstructorOrAssistant(authentication.principal.id, #submissionId)")
    public AssignmentResponse gradeAssignment(String submissionId, BigDecimal grade, String feedback) {
        try {
            StudentAssignment submission = studentAssignmentRepository.findById(submissionId)
                    .orElseThrow(() -> new ResourceNotFoundException("Submission not found"));

            if (grade.compareTo(submission.getAssignment().getMaxPoints()) > 0) {
                throw new BadRequestException("Grade cannot exceed max points");
            }

            submission.setGrade(grade);
            submission.setFeedback(feedback);
            submission = studentAssignmentRepository.save(submission);

            return mapStudentAssignmentToResponse(submission);
        } catch (ResourceNotFoundException e) {
            log.error("Submission not found: {}", submissionId);
            throw e;
        } catch (BadRequestException e) {
            log.warn("Invalid grade: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Failed to grade assignment", e);
            throw new ServiceException("Failed to grade assignment: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @assignmentRepository.findById(#assignmentId).orElseThrow().lesson.id)")
    public void deleteAssignment(String assignmentId) {
        try {
            Assignment assignment = assignmentRepository.findById(assignmentId)
                    .orElseThrow(() -> new ResourceNotFoundException("Assignment not found"));

            // Delete all related submissions first
            studentAssignmentRepository.deleteByAssignmentId(assignmentId);
            assignmentRepository.delete(assignment);
        } catch (ResourceNotFoundException e) {
            log.error("Assignment not found: {}", assignmentId);
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete assignment", e);
            throw new ServiceException("Failed to delete assignment", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('ADMIN') or @securityService.isLessonInstructorOrAssistant(authentication, @assignmentRepository.findById(#request.assignmentId).orElseThrow().lesson.id)")
    public AssignmentResponse updateAssignment(AssignmentUpdateRequest request) {
        try {
            Assignment assignment = assignmentRepository.findById(request.getAssignmentId())
                    .orElseThrow(() -> new ResourceNotFoundException("Assignment not found"));

            if (request.getTitle() != null) {
                assignment.setTitle(request.getTitle());
            }
            if (request.getDescription() != null) {
                assignment.setDescription(request.getDescription());
            }
            if (request.getDueDate() != null) {
                assignment.setDueDate(request.getDueDate());
            }
            if (request.getMaxPoints() != null) {
                assignment.setMaxPoints(request.getMaxPoints());
            }

            assignment = assignmentRepository.save(assignment);
            return mapAssignmentToResponse(assignment);
        } catch (ResourceNotFoundException e) {
            log.error("Assignment not found: {}", request.getAssignmentId());
            throw e;
        } catch (Exception e) {
            log.error("Failed to update assignment", e);
            throw new ServiceException("Failed to update assignment", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private AssignmentResponse mapAssignmentToResponse(Assignment assignment) {
        return AssignmentResponse.builder()
                .id(assignment.getId())
                .lessonId(assignment.getLesson().getId())
                .title(assignment.getTitle())
                .description(assignment.getDescription())
                .dueDate(assignment.getDueDate())
                .maxPoints(assignment.getMaxPoints())
                .build();
    }

    private AssignmentResponse mapStudentAssignmentToResponse(StudentAssignment submission) {
        return AssignmentResponse.builder()
                .id(submission.getId())
                .assignmentId(submission.getAssignment().getId())
                .studentId(submission.getStudent().getId())
                .submissionText(submission.getSubmissionText())
                .submissionDate(submission.getSubmissionDate())
                .grade(submission.getGrade())
                .feedback(submission.getFeedback())
                .build();
    }

    private AssignmentSubmissionResponse mapStudentAssignmentToSubmissionResponse(StudentAssignment submission) {
        return AssignmentSubmissionResponse.builder()
                .submissionId(submission.getId())
                .studentId(submission.getStudent().getId())
                .studentName(submission.getStudent().getFullname())
                .submissionDate(submission.getSubmissionDate())
                .grade(submission.getGrade())
                .build();
    }
}