package com.Pro.Pro.dto.request;

import com.Pro.Pro.model.QuestionType;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExamQuestionRequest {
    @NotBlank
    private String questionText;

    @NotNull
    private QuestionType questionType;

    @NotNull @DecimalMin("0.00")
    private BigDecimal points;

    private List<ExamAnswerRequest> answers;

}