package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.LoginRequest;
import com.Pro.Pro.dto.request.SignupRequest;
import com.Pro.Pro.dto.response.JwtResponse;
import com.Pro.Pro.dto.response.UserResponse;
import com.Pro.Pro.exception.CustomException;
import com.Pro.Pro.exception.ResourceNotFoundException;
import com.Pro.Pro.model.Role;
import com.Pro.Pro.model.User;
import com.Pro.Pro.repository.UserRepository;
import com.Pro.Pro.security.JwtUtils;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.security.UserDetailsServiceImpl;
import com.Pro.Pro.service.AuthService;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final AuthenticationManager authenticationManager;
    private final UserDetailsServiceImpl userDetailsService;

    @Override
    @Transactional
    public JwtResponse authenticateUser(LoginRequest loginRequest, HttpServletResponse response) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getEmail(),
                            loginRequest.getPassword()
                    )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtUtils.generateJwtToken(authentication);

            // Set HttpOnly cookie
            jwtUtils.setJwtCookie(response, jwt, false); // false if on localhost http

            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            List<String> roles = userDetails.getAuthorities().stream()
                    .map(GrantedAuthority::getAuthority)
                    .collect(Collectors.toList());

            return JwtResponse.builder()
                    .token(jwt)
                    .id(userDetails.getId())
                    .email(userDetails.getEmail())
                    .roles(roles)
                    .build();
        } catch (Exception e) {
            throw new BadCredentialsException("Invalid email or password");
        }
    }

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response) {
        // Clear Spring Security context
        SecurityContextHolder.clearContext();

        // Invalidate the HTTP session (clears JSESSIONID)
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }

        // Clear JWT cookie
        Cookie jwtCookie = new Cookie("jwt", null);
        jwtCookie.setHttpOnly(true);
        jwtCookie.setSecure(request.isSecure()); // true in production, false locally
        jwtCookie.setPath("/");
        jwtCookie.setMaxAge(0);
        response.addCookie(jwtCookie);

        // Clear JSESSIONID cookie
        Cookie sessionCookie = new Cookie("JSESSIONID", null);
        sessionCookie.setHttpOnly(true);
        sessionCookie.setSecure(request.isSecure());
        sessionCookie.setPath(request.getContextPath());
        sessionCookie.setMaxAge(0);
        response.addCookie(sessionCookie);

        // Clear any other auth cookies you might have
        Cookie roleCookie = new Cookie("user_role", null);
        roleCookie.setPath("/");
        roleCookie.setMaxAge(0);
        response.addCookie(roleCookie);
    }

    @Override
    @Transactional
    public UserResponse registerStudent(SignupRequest signUpRequest) {

        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            throw new CustomException("البريد الإلكتروني مستخدم بالفعل.", HttpStatus.BAD_REQUEST);
        }

        if (userRepository.existsByNationalId(signUpRequest.getNationalId())) {
            throw new CustomException("هذا الرقم متكرر برجاء اختيار رقم اخر.", HttpStatus.BAD_REQUEST);
        }

        if (signUpRequest.getEmail() == null || signUpRequest.getEmail().isBlank()) {
            throw new CustomException("البريد الإلكتروني مطلوب.", HttpStatus.BAD_REQUEST);
        }

        if (!signUpRequest.getEmail().matches("\\A\\p{ASCII}*\\z")) {
            throw new CustomException("يجب أن يحتوي البريد الإلكتروني على حروف إنجليزية فقط.", HttpStatus.BAD_REQUEST);
        }


        // تحقق من كلمة المرور
        if (signUpRequest.getPassword() == null || signUpRequest.getPassword().isBlank()) {
            throw new CustomException("كلمة المرور مطلوبة.", HttpStatus.BAD_REQUEST);
        }

        if (signUpRequest.getPassword().length() < 8) {
            throw new CustomException("كلمة المرور يجب أن تكون 8 أحرف على الأقل.", HttpStatus.BAD_REQUEST);
        }

        // تحقق من اسم المستخدم
        if (signUpRequest.getUsername() == null || signUpRequest.getUsername().isBlank()) {
            throw new CustomException("اسم المستخدم مطلوب.", HttpStatus.BAD_REQUEST);
        }

        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            throw new CustomException("اسم المستخدم مستخدم بالفعل.", HttpStatus.BAD_REQUEST);
        }

        // الاسم الكامل
        if (signUpRequest.getFullname() == null || signUpRequest.getFullname().isBlank()) {
            throw new CustomException("الاسم الكامل مطلوب.", HttpStatus.BAD_REQUEST);
        }

        if (signUpRequest.getFullname().length() < 3) {
            throw new CustomException("الاسم الكامل يجب ألا يقل عن 3 أحرف.", HttpStatus.BAD_REQUEST);
        }

        // رقم الهاتف
        if (signUpRequest.getPhoneNumber() == null || signUpRequest.getPhoneNumber().isBlank()) {
            throw new CustomException("رقم الهاتف مطلوب.", HttpStatus.BAD_REQUEST);
        }

        // رقم ولي الأمر
        if (signUpRequest.getParentPhoneNumber() == null || signUpRequest.getParentPhoneNumber().isBlank()) {
            throw new CustomException("رقم هاتف ولي الأمر مطلوب.", HttpStatus.BAD_REQUEST);
        }

        // الرقم القومي
        if (signUpRequest.getNationalId() == null || signUpRequest.getNationalId().isBlank()) {
            throw new CustomException("الرقم القومي مطلوب.", HttpStatus.BAD_REQUEST);
        }

        if (!signUpRequest.getNationalId().matches("\\d{14}")) {
            throw new CustomException("الرقم القومي يجب أن يتكون من 14 رقمًا.", HttpStatus.BAD_REQUEST);
        }

        if (userRepository.existsByNationalId(signUpRequest.getNationalId())) {
            throw new CustomException("الرقم القومي مستخدم بالفعل.", HttpStatus.BAD_REQUEST);
        }

        // المحافظة
        if (signUpRequest.getGovernment() == null || signUpRequest.getGovernment().isBlank()) {
            throw new CustomException("اسم المحافظة مطلوب.", HttpStatus.BAD_REQUEST);
        }

        if (signUpRequest.getGovernment().length() < 3) {
            throw new CustomException("اسم المحافظة يجب ألا يقل عن 3 أحرف.", HttpStatus.BAD_REQUEST);
        }

        // تاريخ الميلاد
        if (signUpRequest.getDateOfBirth() == null) {
            throw new CustomException("تاريخ الميلاد مطلوب.", HttpStatus.BAD_REQUEST);
        }

        User user = User.builder()
                .email(signUpRequest.getEmail())
                .password(passwordEncoder.encode(signUpRequest.getPassword()))
                .username(signUpRequest.getUsername())
                .fullname(signUpRequest.getFullname())
                .phoneNumber(signUpRequest.getPhoneNumber())
                .parentPhoneNumber(signUpRequest.getParentPhoneNumber())
                .dateOfBirth(signUpRequest.getDateOfBirth())
                .nationalId(signUpRequest.getNationalId())
                .government(signUpRequest.getGovernment())
                .role(Role.STUDENT)
                .build();

        user = userRepository.save(user);
        return mapUserToResponse(user);
    }

//    @Override
//    public void logout() {
//        SecurityContextHolder.clearContext();
//    }

    @Override
    @Transactional(readOnly = true)
    public UserResponse getCurrentUser(UserDetailsImpl userDetails) {
        if (userDetails == null) {
            throw new CustomException("User not authenticated", HttpStatus.UNAUTHORIZED);
        }
        User user = userRepository.findById(userDetails.getId())
                .orElseThrow(() -> new ResourceNotFoundException("المستخدم غير موجود"));
        return mapUserToResponse(user);
    }

    // In AuthServiceImpl.java
    @Override
    @Transactional
    public JwtResponse refreshToken(String token, HttpServletResponse response) {
        try {
            if (!jwtUtils.validateJwtToken(token, true)) {
                throw new CustomException("Invalid or expired refresh token", HttpStatus.UNAUTHORIZED);
            }

            String username = jwtUtils.getUserNameFromJwtToken(token);
            UserDetails userDetails = userDetailsService.loadUserByUsername(username);

            UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null,
                            userDetails.getAuthorities());

            String newJwt = jwtUtils.generateJwtToken(authentication);

            // Set the new cookie
            jwtUtils.setJwtCookie(response,newJwt, false); // false if on localhost http

            return JwtResponse.builder()
                    .token(newJwt)
                    .id(((UserDetailsImpl) userDetails).getId())
                    .email(userDetails.getUsername())
                    .roles(userDetails.getAuthorities().stream()
                            .map(GrantedAuthority::getAuthority)
                            .collect(Collectors.toList()))
                    .build();
        } catch (UsernameNotFoundException e) {
            throw new CustomException("User not found", HttpStatus.UNAUTHORIZED);
        } catch (Exception e) {
            throw new CustomException("Failed to refresh token", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private UserResponse mapUserToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .username(user.getUsername())
                .fullname(user.getFullname())
                .phoneNumber(user.getPhoneNumber())
                .parentPhoneNumber(user.getParentPhoneNumber())
                .dateOfBirth(user.getDateOfBirth())
                .nationalId(user.getNationalId())
                .government(user.getGovernment())
                .avatarUrl(user.getAvatarUrl())
                .role(user.getRole().name())
                .instructorName(user.getInstructor() != null ? user.getInstructor().getEmail() : null)
                .createdAt(user.getCreatedAt())
                .build();
    }
}