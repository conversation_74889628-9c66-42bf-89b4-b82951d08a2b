package com.Pro.Pro.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErrorResponse {
    private HttpStatus status;
    private String message;
    private LocalDateTime timestamp = LocalDateTime.now();
    public ErrorResponse(HttpStatus httpStatus, String message) {
    }
}