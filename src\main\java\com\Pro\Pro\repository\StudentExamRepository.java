package com.Pro.Pro.repository;

import com.Pro.Pro.model.ExamSubmission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StudentExamRepository extends JpaRepository<ExamSubmission, String> {
    List<ExamSubmission> findByExamId(String examId);
    boolean existsByStudentIdAndExamId(String studentId, String examId);
}
