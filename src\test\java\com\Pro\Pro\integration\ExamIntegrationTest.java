package com.Pro.Pro.integration;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.Pro.Pro.dto.request.ExamQuestionRequest;
import com.Pro.Pro.dto.request.ExamRequest;
import com.Pro.Pro.dto.response.ExamResponse;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.ExamService;
import com.Pro.Pro.service.LessonProgressService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ExamIntegrationTest {

    @Autowired
    private ExamService examService;

    @Autowired
    private LessonProgressService progressService;

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private StudentLessonRepository studentLessonRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private User instructor;
    private User student;
    private Course course;
    private Lesson lesson;

    @BeforeEach
    void setUp() {
        // Set up security context for instructor
        UsernamePasswordAuthenticationToken instructorAuth = new UsernamePasswordAuthenticationToken(
                "instructor", "password",
                Arrays.asList(new SimpleGrantedAuthority("ROLE_INSTRUCTOR"), new SimpleGrantedAuthority("ROLE_ADMIN"))
        );
        SecurityContextHolder.getContext().setAuthentication(instructorAuth);

        // Create instructor
        instructor = User.builder()
                .username("instructor")
                .email("<EMAIL>")
                .password("password")
                .role(Role.INSTRUCTOR)
                .build();
        instructor = userRepository.save(instructor);

        // Create student
        student = User.builder()
                .username("student")
                .email("<EMAIL>")
                .password("password")
                .role(Role.STUDENT)
                .build();
        student = userRepository.save(student);

        // Create course
        course = Course.builder()
                .name("Test Course")
                .description("Test Description")
                .instructor(instructor)
                .build();
        course = courseRepository.save(course);

        // Create lesson
        lesson = Lesson.builder()
                .name("Test Lesson")
                .description("Test Lesson Description")
                .price(new BigDecimal("100.00"))
                .videoUrl("http://test.com/video")
                .course(course)
                .build();
        lesson = lessonRepository.save(lesson);

        // Grant student access to lesson
        StudentLesson studentLesson = StudentLesson.builder()
                .student(student)
                .lesson(lesson)
                .paymentMethod(PaymentMethod.FAWRY)
                .paymentReference("test-ref")
                .paymentDate(new Date())
                .accessExpiryDate(new Date(System.currentTimeMillis() + 86400000)) // 1 day
                .progressStatus(LessonProgressStatus.PURCHASED)
                .build();
        studentLessonRepository.save(studentLesson);
    }

    @Test
    public void testCreateMixedTypeExamAndSubmit() {
        // Create exam with mixed question types
        ExamRequest examRequest = createMixedTypeExamRequest();

        // Create exam
        ExamResponse examResponse = examService.createExam(examRequest, lesson.getId());

        assertNotNull(examResponse);
        assertNotNull(examResponse.getId());
        assertEquals("Mixed Type Programming Exam", examResponse.getTitle());
        assertEquals(3, examResponse.getQuestions().size());

        // Verify exam was saved to database
        Exam savedExam = examRepository.findById(examResponse.getId()).orElse(null);
        assertNotNull(savedExam);
        assertEquals(3, savedExam.getQuestions().size());

        // Test submission with correct answers
        Map<String, String> correctAnswers = prepareCorrectAnswers(savedExam);

        // Switch to student authentication for submission
        UsernamePasswordAuthenticationToken studentAuth = new UsernamePasswordAuthenticationToken(
                "student", "password",
                Arrays.asList(new SimpleGrantedAuthority("ROLE_STUDENT"))
        );
        SecurityContextHolder.getContext().setAuthentication(studentAuth);

        ExamResponse submissionResponse = examService.submitExam(
                savedExam.getId(),
                correctAnswers,
                student.getId()
        );

        assertNotNull(submissionResponse);
        assertTrue(submissionResponse.isPassed());
        assertEquals(new BigDecimal("100.00"), submissionResponse.getScore());

        // Verify lesson progress was updated
        StudentLesson updatedStudentLesson = studentLessonRepository
                .findByStudentIdAndLessonId(student.getId(), lesson.getId())
                .orElse(null);
        assertNotNull(updatedStudentLesson);
        assertEquals(LessonProgressStatus.EXAM_PASSED, updatedStudentLesson.getProgressStatus());
    }

    @Test
    public void testSubmitExamWithPartialCorrectAnswers() {
        // Create exam
        ExamRequest examRequest = createMixedTypeExamRequest();
        ExamResponse examResponse = examService.createExam(examRequest, lesson.getId());

        Exam savedExam = examRepository.findById(examResponse.getId()).orElse(null);
        assertNotNull(savedExam);

        // Test submission with partial correct answers (should fail)
        Map<String, String> partialAnswers = preparePartialAnswers(savedExam);

        // Switch to student authentication for submission
        UsernamePasswordAuthenticationToken studentAuth = new UsernamePasswordAuthenticationToken(
                "student", "password",
                Arrays.asList(new SimpleGrantedAuthority("ROLE_STUDENT"))
        );
        SecurityContextHolder.getContext().setAuthentication(studentAuth);

        ExamResponse submissionResponse = examService.submitExam(
                savedExam.getId(),
                partialAnswers,
                student.getId()
        );

        assertNotNull(submissionResponse);
        assertFalse(submissionResponse.isPassed());
        assertTrue(submissionResponse.getScore().compareTo(new BigDecimal("75.00")) < 0);

        // Verify lesson progress was NOT updated to EXAM_PASSED
        StudentLesson updatedStudentLesson = studentLessonRepository
                .findByStudentIdAndLessonId(student.getId(), lesson.getId())
                .orElse(null);
        assertNotNull(updatedStudentLesson);
        assertEquals(LessonProgressStatus.PURCHASED, updatedStudentLesson.getProgressStatus());
    }

    private ExamRequest createMixedTypeExamRequest() {
        List<ExamQuestionRequest> questions = Arrays.asList(
                // Multiple choice question
                ExamQuestionRequest.builder()
                        .questionText("Which are object-oriented programming languages?")
                        .questionType(QuestionType.MULTIPLE_CHOICE)
                        .points(new BigDecimal("30.00"))
                        .answers(Arrays.asList(
                                ExamAnswerRequest.builder().answerText("Java").correct(true).build(),
                                ExamAnswerRequest.builder().answerText("Python").correct(true).build(),
                                ExamAnswerRequest.builder().answerText("C").correct(false).build(),
                                ExamAnswerRequest.builder().answerText("Assembly").correct(false).build()
                        ))
                        .build(),
                // Single choice question
                ExamQuestionRequest.builder()
                        .questionText("What is the default access modifier in Java?")
                        .questionType(QuestionType.SINGLE_CHOICE)
                        .points(new BigDecimal("40.00"))
                        .answers(Arrays.asList(
                                ExamAnswerRequest.builder().answerText("package-private").correct(true).build(),
                                ExamAnswerRequest.builder().answerText("public").correct(false).build(),
                                ExamAnswerRequest.builder().answerText("private").correct(false).build(),
                                ExamAnswerRequest.builder().answerText("protected").correct(false).build()
                        ))
                        .build(),
                // True/False question
                ExamQuestionRequest.builder()
                        .questionText("Java supports multiple inheritance through classes.")
                        .questionType(QuestionType.TRUE_FALSE)
                        .points(new BigDecimal("30.00"))
                        .answers(Arrays.asList(
                                ExamAnswerRequest.builder().answerText("true").correct(false).build(),
                                ExamAnswerRequest.builder().answerText("false").correct(true).build()
                        ))
                        .build()
        );

        return ExamRequest.builder()
                .title("Mixed Type Programming Exam")
                .passingScore(new BigDecimal("75.00"))
                .timeLimitMinutes(60)
                .questions(questions)
                .build();
    }

    private Map<String, String> prepareCorrectAnswers(Exam exam) {
        Map<String, String> answers = new HashMap<>();

        for (ExamQuestion question : exam.getQuestions()) {
            switch (question.getQuestionType()) {
                case MULTIPLE_CHOICE:
                    // Get all correct answer IDs and join with comma
                    String correctIds = question.getAnswers().stream()
                            .filter(ExamAnswer::isCorrect)
                            .map(ExamAnswer::getId)
                            .reduce((a, b) -> a + "," + b)
                            .orElse("");
                    answers.put(question.getId(), correctIds);
                    break;

                case SINGLE_CHOICE:
                    // Get the single correct answer ID
                    String correctId = question.getAnswers().stream()
                            .filter(ExamAnswer::isCorrect)
                            .map(ExamAnswer::getId)
                            .findFirst()
                            .orElse("");
                    answers.put(question.getId(), correctId);
                    break;

                case TRUE_FALSE:
                    // Get the correct true/false text
                    String correctText = question.getAnswers().stream()
                            .filter(ExamAnswer::isCorrect)
                            .map(ExamAnswer::getAnswerText)
                            .findFirst()
                            .orElse("");
                    answers.put(question.getId(), correctText);
                    break;
            }
        }

        return answers;
    }

    private Map<String, String> preparePartialAnswers(Exam exam) {
        Map<String, String> answers = new HashMap<>();

        for (ExamQuestion question : exam.getQuestions()) {
            switch (question.getQuestionType()) {
                case MULTIPLE_CHOICE:
                    // Submit only one correct answer (should be wrong for multiple choice)
                    String oneCorrectId = question.getAnswers().stream()
                            .filter(ExamAnswer::isCorrect)
                            .map(ExamAnswer::getId)
                            .findFirst()
                            .orElse("");
                    answers.put(question.getId(), oneCorrectId);
                    break;

                case SINGLE_CHOICE:
                    // Submit wrong answer
                    String wrongId = question.getAnswers().stream()
                            .filter(a -> !a.isCorrect())
                            .map(ExamAnswer::getId)
                            .findFirst()
                            .orElse("");
                    answers.put(question.getId(), wrongId);
                    break;

                case TRUE_FALSE:
                    // Submit correct answer (this one should be right)
                    String correctText = question.getAnswers().stream()
                            .filter(ExamAnswer::isCorrect)
                            .map(ExamAnswer::getAnswerText)
                            .findFirst()
                            .orElse("");
                    answers.put(question.getId(), correctText);
                    break;
            }
        }

        return answers;
    }
}
