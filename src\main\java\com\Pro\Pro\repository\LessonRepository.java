package com.Pro.Pro.repository;

import com.Pro.Pro.model.Lesson;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface LessonRepository extends JpaRepository<Lesson, String> {
    List<Lesson> findByCourseId(String courseId);

    @Query("SELECT CASE WHEN COUNT(l) > 0 THEN true ELSE false END " +
            "FROM Lesson l WHERE l.id = :lessonId AND l.course.instructor.id = :instructorId")
    boolean existsByIdAndCourseInstructorId(@Param("lessonId") String lessonId,
                                            @Param("instructorId") String instructorId);

    @Query("SELECT l FROM Lesson l JOIN l.accessCodes ac WHERE ac.code = :code")
    Optional<Lesson> findByAccessCode(@Param("code") String code);

    @Query(value = """
        SELECT l.id, l.name, l.description, l.price, 
               COUNT(sl.id) as student_count 
        FROM lessons l 
        LEFT JOIN student_lessons sl ON l.id = sl.lesson_id 
        WHERE l.course_id = :courseId 
        GROUP BY l.id
        ORDER BY l.created_at
        """, nativeQuery = true)
    List<Object[]> findLessonSummariesByCourseId(@Param("courseId") String courseId);

    @Query("SELECT l FROM Lesson l " +
            "WHERE l.course.id = :courseId " +
            "AND l.price BETWEEN :minPrice AND :maxPrice " +
            "ORDER BY " +
            "CASE WHEN :sortBy = 'name' THEN l.name END, " +
            "CASE WHEN :sortBy = 'price' THEN l.price END, " +
            "CASE WHEN :sortBy = 'createdAt' THEN l.createdAt END")
    Page<Lesson> findFilteredLessons(
            @Param("courseId") String courseId,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice,
            @Param("sortBy") String sortBy,
            Pageable pageable);

    @Query("SELECT COUNT(l) FROM Lesson l WHERE l.course.id = :courseId")
    long countByCourseId(@Param("courseId") String courseId);
}