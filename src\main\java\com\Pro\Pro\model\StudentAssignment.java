package com.Pro.Pro.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "student_assignments", indexes = {
        @Index(name = "idx_student_assignment_student", columnList = "student_id"),
        @Index(name = "idx_student_assignment_assignment", columnList = "assignment_id"),
        @Index(name = "idx_student_assignment_submission", columnList = "submissionDate")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class StudentAssignment extends Auditable {
    @Id
    @GeneratedValue(generator = "nanoid")
    @GenericGenerator(name = "nanoid", strategy = "com.Pro.Pro.NanoIdGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private String id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    @ToString.Exclude
    private User student;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assignment_id", nullable = false)
    @ToString.Exclude
    private Assignment assignment;

    @Column(columnDefinition = "TEXT")
    private String submissionText;

    private Date submissionDate;

    private BigDecimal grade;

    @Column(columnDefinition = "TEXT")
    private String feedback;
}