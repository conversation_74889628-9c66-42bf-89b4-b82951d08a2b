package com.Pro.Pro.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateAssistantRequest {

    @NotBlank
    @Email
    private String email;

    @NotBlank @Size(min = 8)
    private String password;

    @NotBlank
    private String fullname;

    @NotBlank
    private String username;

    @NotBlank
    private String phoneNumber;

    @NotBlank
    private String nationalId;

    @NotBlank
    private String government;

}
