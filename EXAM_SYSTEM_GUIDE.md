# Exam System Guide

## Overview

The exam system supports three types of questions:

- **MULTIPLE_CHOICE**: Questions with multiple correct answers
- **SINGLE_CHOICE**: Questions with exactly one correct answer
- **TRUE_FALSE**: Questions with true/false answers

## Question Types

### 1. Multiple Choice Questions

- Must have at least 2 answers
- Must have at least 1 correct answer
- Must have at least 1 incorrect answer
- Students can select multiple answers
- Scoring: All correct answers must be selected (and no incorrect ones) to get points

**Example:**

```json
{
  "questionText": "Which are programming languages?",
  "questionType": "MULTIPLE_CHOICE",
  "points": 20.0,
  "answers": [
    { "answerText": "Java", "correct": true },
    { "answerText": "Python", "correct": true },
    { "answerText": "HTML", "correct": false },
    { "answerText": "CSS", "correct": false }
  ]
}
```

### 2. Single Choice Questions

- Must have at least 2 answers
- Must have exactly 1 correct answer
- Students can select only one answer
- Scoring: The selected answer must be the correct one to get points

**Example:**

```json
{
  "questionText": "What is the capital of France?",
  "questionType": "SINGLE_CHOICE",
  "points": 10.0,
  "answers": [
    { "answerText": "Paris", "correct": true },
    { "answerText": "London", "correct": false },
    { "answerText": "Berlin", "correct": false }
  ]
}
```

### 3. True/False Questions

- Must have exactly 2 answers: "true" and "false"
- Must have exactly 1 correct answer
- Students select either true or false
- Scoring: The selected answer must match the correct one to get points

**Example:**

```json
{
  "questionText": "Java is platform independent.",
  "questionType": "TRUE_FALSE",
  "points": 5.0,
  "answers": [
    { "answerText": "true", "correct": true },
    { "answerText": "false", "correct": false }
  ]
}
```

## API Endpoints

### Create Exam

**POST** `/api/exams/lessons/{lessonId}`

### Get Exam

**GET** `/api/exams/{examId}`

### Update Exam

**PUT** `/api/exams/{examId}`

### Submit Exam

**POST** `/api/exams/{examId}/submit`

## Exam Submission Format

When submitting exam answers, use the following format:

- **Single Choice**: Submit the answer ID
- **Multiple Choice**: Submit comma-separated answer IDs
- **True/False**: Submit "true" or "false"

**Example submission:**

```json
{
  "examId": "exam-123",
  "answers": [
    {
      "questionId": "question-1",
      "submittedAnswer": "answer-id-1,answer-id-2"
    },
    {
      "questionId": "question-2",
      "submittedAnswer": "answer-id-5"
    },
    {
      "questionId": "question-3",
      "submittedAnswer": "true"
    }
  ]
}
```

## Validation Rules

### Multiple Choice

- ✅ At least 2 answers
- ✅ At least 1 correct answer
- ✅ At least 1 incorrect answer

### Single Choice

- ✅ At least 2 answers
- ✅ Exactly 1 correct answer

### True/False

- ✅ Exactly 2 answers
- ✅ Exactly 1 correct answer
- ✅ Answers must be "true" and "false"

## Error Messages

All validation errors are returned in Arabic with detailed context:

- "يجب أن يحتوي السؤال على إجابات" - Question must contain answers
- "خطأ في السؤال رقم X: ..." - Error in question number X: ...
- "أسئلة الاختيار المتعدد يجب أن تحتوي على إجابتين على الأقل" - Multiple choice questions must contain at least 2 answers
- "أسئلة الصواب/الخطأ يجب أن تحتوي على إجابتين فقط" - True/false questions must contain exactly 2 answers
- "السؤال رقم X مكرر" - Question number X is duplicate
- "درجة النجاح يجب أن تكون بين 0 و 100" - Passing score must be between 0 and 100
- "وقت الامتحان يجب أن يكون أكبر من صفر" - Exam time must be greater than zero

## Lesson Progress Integration

When a student successfully passes an exam (score >= passing score):

- Student's lesson progress is automatically updated to `EXAM_PASSED`
- This unlocks access to the lesson video content
- Progress follows the sequence: `PURCHASED` → `EXAM_PASSED` → `VIDEO_WATCHED` → `ASSIGNMENT_DONE`

## Enhanced Features

- **Mixed Question Types**: Create exams with any combination of question types
- **Comprehensive Validation**: Detailed error messages with question numbers
- **Duplicate Detection**: Prevents duplicate question texts
- **Progress Tracking**: Automatic lesson progress updates
- **Flexible Scoring**: Support for different point values per question
- **Robust Error Handling**: Detailed error messages for debugging

## Examples

Check the `examples/` directory for complete JSON examples:

- `mixed-exam-demo.json` - Comprehensive exam with all question types
- `mixed-exam-submission-demo.json` - Example submission format
