package com.Pro.Pro.dto.response;

import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;


@Data
@Builder
public class LessonResponse {

    private String id;
    private String courseId;
    private String name;
    private String description;
    private String photoUrl;
    private BigDecimal price;
    private String instructorId;
    private String instructorName;
    private Date accessExpiryDate;
    private boolean isExpired;
}