package com.Pro.Pro.dto;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class ExamAnswerRequestJsonTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testJsonDeserializationWithCorrectField() throws Exception {
        // Test JSON with "correct" field (new format)
        String json = "{\"answerText\": \"Java\", \"correct\": true}";
        
        ExamAnswerRequest answer = objectMapper.readValue(json, ExamAnswerRequest.class);
        
        assertEquals("Java", answer.getAnswerText());
        assertTrue(answer.isCorrect());
    }

    @Test
    public void testJsonSerializationWithCorrectField() throws Exception {
        ExamAnswerRequest answer = ExamAnswerRequest.builder()
                .answerText("Python")
                .correct(false)
                .build();
        
        String json = objectMapper.writeValueAsString(answer);
        
        assertTrue(json.contains("\"answerText\":\"Python\""));
        assertTrue(json.contains("\"correct\":false"));
    }

    @Test
    public void testMultipleChoiceAnswersDeserialization() throws Exception {
        String json = "[" +
                "{\"answerText\": \"Java\", \"correct\": true}," +
                "{\"answerText\": \"Python\", \"correct\": true}," +
                "{\"answerText\": \"C\", \"correct\": false}," +
                "{\"answerText\": \"Assembly\", \"correct\": false}" +
                "]";
        
        ExamAnswerRequest[] answers = objectMapper.readValue(json, ExamAnswerRequest[].class);
        
        assertEquals(4, answers.length);
        
        // Check correct answers
        assertTrue(answers[0].isCorrect()); // Java
        assertTrue(answers[1].isCorrect()); // Python
        assertFalse(answers[2].isCorrect()); // C
        assertFalse(answers[3].isCorrect()); // Assembly
        
        // Count correct answers (should be 2 for multiple choice)
        long correctCount = java.util.Arrays.stream(answers)
                .filter(ExamAnswerRequest::isCorrect)
                .count();
        
        assertEquals(2, correctCount);
    }
}
