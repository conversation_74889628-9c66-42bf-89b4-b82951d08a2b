package com.Pro.Pro.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "courses", indexes = {
        @Index(name = "idx_course_name", columnList = "name"),
        @Index(name = "idx_course_instructor", columnList = "instructor_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class Course extends Auditable {
    @Id
    @GeneratedValue(generator = "nanoid")
    @GenericGenerator(name = "nanoid", strategy = "com.Pro.Pro.NanoIdGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private String id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "instructor_id", nullable = false)
    @JsonIgnoreProperties({"courses"})
    @ToString.Exclude
    private User instructor;

    @Column(nullable = false)
    private String name;

    private String description;
    private String photoUrl;

    @OneToMany(mappedBy = "course", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    @JsonManagedReference
    @ToString.Exclude
    private Set<Lesson> lessons = new HashSet<>();
}