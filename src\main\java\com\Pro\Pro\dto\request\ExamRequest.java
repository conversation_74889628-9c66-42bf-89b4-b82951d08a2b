package com.Pro.Pro.dto.request;

import com.Pro.Pro.dto.payload.ExamValidationMessages;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExamRequest {
    @NotBlank(message = ExamValidationMessages.TITLE_REQUIRED)
    private String title;

    @NotNull(message = ExamValidationMessages.PASSING_SCORE_RANGE)
    @DecimalMin(value = "0.00", message = ExamValidationMessages.PASSING_SCORE_RANGE)
    @DecimalMax(value = "100.00", message = ExamValidationMessages.PASSING_SCORE_RANGE)
    private BigDecimal passingScore;

    @NotNull(message = ExamValidationMessages.TIME_LIMIT_POSITIVE)
    @Min(value = 1, message = ExamValidationMessages.TIME_LIMIT_POSITIVE)
    private Integer timeLimitMinutes;

    @Valid
    @Size(min = 1, message = "يجب أن يحتوي الامتحان على سؤال واحد على الأقل")
    private List<ExamQuestionRequest> questions;
}