package com.Pro.Pro.dto.request;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssignmentRequest {
    @NotBlank
    private String title;

    private String description;

    @Future
    private Date dueDate;

    @NotNull
    @DecimalMin("0.00")
    private BigDecimal maxPoints;
}
