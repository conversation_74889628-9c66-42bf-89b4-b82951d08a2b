package com.Pro.Pro.controller;

import com.Pro.Pro.dto.payload.LessonPayload;
import com.Pro.Pro.dto.request.InstructorProfileWithCoursesResponse;
import com.Pro.Pro.dto.request.LessonAccessRequest;
import com.Pro.Pro.dto.request.UpdateProfileRequest;
import com.Pro.Pro.dto.response.CourseResponse;
import com.Pro.Pro.dto.response.LessonResponse;
import com.Pro.Pro.dto.response.PaymentResponse;
import com.Pro.Pro.dto.response.UserResponse;
import com.Pro.Pro.model.InstructorProfile;
import com.Pro.Pro.security.CurrentUser;
import com.Pro.Pro.security.UserDetailsImpl;
import com.Pro.Pro.service.PaymentService;
import com.Pro.Pro.service.StudentService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
@Slf4j
@RestController
@RequestMapping("/api/students")
@RequiredArgsConstructor
public class StudentController {
    @Autowired
    private final StudentService studentService;

    @PutMapping("/{studentId}/profile")
    @PreAuthorize("#studentId == authentication.principal.id")
    public ResponseEntity<UserResponse> updateProfile(
            @PathVariable String studentId,
            @Valid @RequestBody UpdateProfileRequest request) {
        return ResponseEntity.ok(studentService.updateProfile(studentId, request));
    }

    @PostMapping("/lessons/{lessonId}/pay/fawry")
    public ResponseEntity<PaymentResponse> payWithFawry(
            @PathVariable String lessonId,
            @CurrentUser UserDetailsImpl currentUser) {
        return ResponseEntity.ok(studentService.payWithFawry(lessonId, currentUser.getId()));
    }

    @GetMapping("/instructors/{instructorId}/profile")
    public ResponseEntity<InstructorProfile> getInstructorProfile(@PathVariable String instructorId) {
        return ResponseEntity.ok(studentService.getInstructorProfile(instructorId));
    }

    @GetMapping("/instructors/{instructorId}/courses")
    public ResponseEntity<List<CourseResponse>> getInstructorCourses(@PathVariable String instructorId) {
        return ResponseEntity.ok(studentService.getInstructorCourses(instructorId));
    }

    @GetMapping("/instructors/{instructorId}/full-profile")
    public ResponseEntity<InstructorProfileWithCoursesResponse> getInstructorProfileWithCourses(
            @PathVariable String instructorId) {
        return ResponseEntity.ok(studentService.getInstructorProfileWithCourses(instructorId));
    }

    @GetMapping("/lessons/{lessonId}")
    @PreAuthorize("@securityService.isStudentOwner(authentication.principal.id, #lessonId)")
    public ResponseEntity<LessonPayload> getLessonDetails(
            @PathVariable String lessonId,
            @CurrentUser UserDetailsImpl currentUser) {
        return ResponseEntity.ok(studentService.getLessonDetails(lessonId, currentUser.getId()));
    }


    @GetMapping("/my-lessons")
//    @PreAuthorize("hasRole('STUDENT')")
    public ResponseEntity<List<LessonResponse>> getPaidLessons(@CurrentUser UserDetailsImpl currentUser) {
        return ResponseEntity.ok(studentService.getPaidLessons(currentUser.getId()));
    }
}