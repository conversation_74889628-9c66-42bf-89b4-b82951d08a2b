package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.payload.LessonPayload;
import com.Pro.Pro.dto.request.InstructorProfileWithCoursesResponse;
import com.Pro.Pro.dto.request.UpdateProfileRequest;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.ExamNotPassedException;
import com.Pro.Pro.exception.LessonNotPurchasedException;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.LessonProgressService;
import com.Pro.Pro.service.StudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StudentServiceImpl implements StudentService {

    private static final int ACCESS_DURATION_DAYS = 14;
    private final UserRepository userRepository;
    private final InstructorProfileRepository instructorProfileRepository;
    private final CourseRepository courseRepository;
    private final LessonRepository lessonRepository;
    private final StudentLessonRepository studentLessonRepository;
    private final FawryPaymentRepository fawryPaymentRepository;
    private final LessonProgressServiceImpl progressService;

    @Override
    public UserResponse updateProfile(String studentId, UpdateProfileRequest request) {
        User student = userRepository.findByIdAndRole(studentId, Role.STUDENT)
                .orElseThrow(() -> new ResourceNotFoundException("Student not found"));

        if (request.getPhoneNumber() != null) {
            student.setPhoneNumber(request.getPhoneNumber());
        }
        if (request.getAvatarUrl() != null) {
            student.setAvatarUrl(request.getAvatarUrl());
        }

        student = userRepository.save(student);
        return mapUserToResponse(student);
    }

    @Override
    @Transactional(readOnly = true)
    public InstructorProfileWithCoursesResponse getInstructorProfileWithCourses(String instructorId) {
        // Get instructor profile
        InstructorProfile profile = instructorProfileRepository.findByUserId(instructorId)
                .orElseThrow(() -> new ResourceNotFoundException("Instructor profile not found"));

        // Get courses with lessons (using JOIN FETCH to avoid N+1 problem)
        List<Course> courses = courseRepository.findByInstructorIdWithLessons(instructorId);

        User instructorUser = userRepository.findById(instructorId).orElseThrow(() -> new ResourceNotFoundException("Instructor not found"));

        // Map to response
        return InstructorProfileWithCoursesResponse.builder()
                .id(instructorUser.getId())
                .bio(profile.getBio())
                .fullname(instructorUser.getFullname())
                .photoUrl(profile.getPhotoUrl())
                .courses(courses.stream()
                        .map(this::mapToSimpleCourseResponse)
                        .collect(Collectors.toList()))
                .build();
    }

    private InstructorProfileWithCoursesResponse.SimpleCourseResponse mapToSimpleCourseResponse(Course course) {
        return InstructorProfileWithCoursesResponse.SimpleCourseResponse.builder()
                .id(course.getId())
                .name(course.getName())
                .description(course.getDescription())
                .photoUrl(course.getPhotoUrl())
                .lessons(course.getLessons().stream()
                        .map(this::mapToSimpleLessonResponse)
                        .collect(Collectors.toList()))
                .build();
    }

    private InstructorProfileWithCoursesResponse.SimpleLessonResponse mapToSimpleLessonResponse(Lesson lesson) {
        return InstructorProfileWithCoursesResponse.SimpleLessonResponse.builder()
                .id(lesson.getId())
                .name(lesson.getName())
                .description(lesson.getDescription())
                .photoUrl(lesson.getPhotoUrl())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public InstructorProfile getInstructorProfile(String instructorId) {
        return instructorProfileRepository.findByUserId(instructorId)
                .orElseThrow(() -> new ResourceNotFoundException("Instructor profile not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<CourseResponse> getInstructorCourses(String instructorId) {
        return courseRepository.findByInstructorId(instructorId).stream()
                .map(this::mapCourseToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public LessonPayload getLessonDetails(String lessonId, String studentId) {
        Lesson lesson = lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("Lesson not found"));

        StudentLesson studentLesson = studentLessonRepository.findByStudentIdAndLessonId(studentId, lessonId)
                .orElseThrow();

        boolean hasExam = lesson.getExam() != null;
        LessonProgressStatus progress = studentLesson.getProgressStatus();

        // Determine content access
        boolean canAccessVideo = !hasExam || progress.ordinal() >= LessonProgressStatus.EXAM_PASSED.ordinal();
        boolean canAccessAssignment = progress.ordinal() >= LessonProgressStatus.VIDEO_WATCHED.ordinal();

        String accessError = !canAccessVideo ?
                "Complete and pass the exam to unlock video content" : null;

        return LessonPayload.builder()
                .lesson(mapLessonToResponse(lesson))
                .exam(hasExam ? mapExamToResponse(lesson.getExam()) : null)
                .assignment(lesson.getAssignment() != null ?
                        mapAssignmentToResponse(lesson.getAssignment()) : null)
                .hasAccess(true)
                .videoUrl(canAccessVideo ? lesson.getVideoUrl() : null)
                .accessError(accessError)
                .progressStatus(progress)
                .canAccessVideo(canAccessVideo)
                .canAccessExam(true) // Always allowed if purchased
                .canAccessAssignment(canAccessAssignment)
                .build();
    }

    private boolean canAccessVideoContent(String studentId, String lessonId, Lesson lesson) {
        if (lesson.getExam() == null) return true;
        try {
            progressService.validateLessonAccess(studentId, lessonId, LessonProgressService.LessonPart.VIDEO);
            return true;
        } catch (ExamNotPassedException e) {
            return false;
        }
    }

    private String getAccessErrorMessage(String studentId, String lessonId, Lesson lesson) {
        if (lesson.getExam() == null) return null;

        StudentLesson record = studentLessonRepository.findByStudentIdAndLessonId(studentId, lessonId)
                .orElseThrow(() -> new LessonNotPurchasedException("You need to purchase this lesson first"));

        if (record.getProgressStatus().ordinal() < LessonProgressStatus.EXAM_PASSED.ordinal()) {
            return "You must pass the exam first to access the video content";
        }
        return null;
    }

    @Override
    @Transactional
    public PaymentResponse payWithFawry(String lessonId, String studentId) {
        Lesson lesson = lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("Lesson not found"));

        User student = userRepository.findByIdAndRole(studentId, Role.STUDENT)
                .orElseThrow(() -> new ResourceNotFoundException("Student not found"));

        String reference = "FAWRY-" + UUID.randomUUID().toString().substring(0, 8);

        FawryPayment payment = FawryPayment.builder()
                .student(student)
                .lesson(lesson)
                .referenceNumber(reference)
                .amount(lesson.getPrice())
                .status(PaymentStatus.PENDING)
                .build();

        fawryPaymentRepository.save(payment);

        return PaymentResponse.builder()
                .status("PENDING")
                .referenceNumber(reference)
                .paymentUrl("https://fawry.example.com/pay?ref=" + reference)
                .lessonId(lesson.getId())
                .lessonName(lesson.getName())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public List<LessonResponse> getPaidLessons(String studentId) {
        return studentLessonRepository.findByStudentId(studentId).stream()
                .map(this::mapLessonToResponse)
                .collect(Collectors.toList());
    }

    // Additional helper methods for mapping entities to DTOs
    private UserResponse mapUserToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .phoneNumber(user.getPhoneNumber())
                .parentPhoneNumber(user.getParentPhoneNumber())
                .dateOfBirth(user.getDateOfBirth())
                .nationalId(user.getNationalId())
                .government(user.getGovernment())
                .avatarUrl(user.getAvatarUrl())
                .role(user.getRole().name())
                .createdAt(user.getCreatedAt())
                .build();
    }

    private CourseResponse mapCourseToResponse(Course course) {
        return CourseResponse.builder()
                .id(course.getId())
                .name(course.getName())
                .description(course.getDescription())
                .photoUrl(course.getPhotoUrl())
                .instructorId(course.getInstructor().getId())
                .instructorName(course.getInstructor().getEmail()) // Or proper name if available
                .createdAt(course.getCreatedAt())
                .lessonCount(course.getLessons() != null ? course.getLessons().size() : 0)
                .build();
    }

    private LessonResponse mapLessonToResponse(Lesson lesson) {
        return LessonResponse.builder()
                .id(lesson.getId())
                .courseId(lesson.getCourse().getId())
                .name(lesson.getName())
                .description(lesson.getDescription())
                .photoUrl(lesson.getPhotoUrl())
                .price(lesson.getPrice())
                .build();
    }

    private LessonResponse mapLessonToResponse(StudentLesson studentLesson) {
        Lesson lesson = studentLesson.getLesson();

        // Calculate if the lesson access has expired
        boolean isExpired = studentLesson.getAccessExpiryDate() != null &&
                studentLesson.getAccessExpiryDate().before(new Date());

        return LessonResponse.builder()
                .id(lesson.getId())
                .courseId(lesson.getCourse() != null ? lesson.getCourse().getId() : null)
                .name(lesson.getName())
                .description(lesson.getDescription())
                .photoUrl(lesson.getPhotoUrl())
                .price(lesson.getPrice())
                .instructorName(lesson.getCourse() != null && lesson.getCourse().getInstructor() != null
                        ? lesson.getCourse().getInstructor().getFullname()
                        : null)
                .instructorId(lesson.getCourse() != null && lesson.getCourse().getInstructor() != null
                        ? lesson.getCourse().getInstructor().getId()
                        : null)
                .accessExpiryDate(studentLesson.getAccessExpiryDate())
                .isExpired(isExpired)  // Add the expired status
                .build();
    }

    private Date calculateExpiryDate() {
        return Date.from(LocalDate.now()
                .plusDays(ACCESS_DURATION_DAYS)
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant());
    }

    private ExamQuestionResponse mapQuestionToResponse(ExamQuestion question) {
        return ExamQuestionResponse.builder()
                .id(question.getId())
                .questionText(question.getQuestionText())
                .questionType(question.getQuestionType())
                .points(question.getPoints())
                .answers(question.getAnswers().stream()
                        .map(this::mapAnswerToResponse)
                        .collect(Collectors.toList()))
                .build();
    }

    private ExamAnswerResponse mapAnswerToResponse(ExamAnswer answer) {
        return ExamAnswerResponse.builder()
                .id(answer.getId())
                .answerText(answer.getAnswerText())
                .isCorrect(answer.isCorrect())
                .build();
    }

    private ExamResponse mapExamToResponse(Exam exam) {
        return ExamResponse.builder()
                .id(exam.getId())
                .lessonId(exam.getLesson().getId())
                .title(exam.getTitle())
                .passingScore(exam.getPassingScore())
                .questions(exam.getQuestions().stream()
                        .map(this::mapQuestionToResponse)
                        .collect(Collectors.toList()))
                .build();
    }

    private AssignmentResponse mapAssignmentToResponse(Assignment assignment) {
        return AssignmentResponse.builder()
                .id(assignment.getId())
                .lessonId(assignment.getLesson().getId())
                .title(assignment.getTitle())
                .description(assignment.getDescription())
                .dueDate(assignment.getDueDate())
                .maxPoints(assignment.getMaxPoints())
                .build();
    }

}