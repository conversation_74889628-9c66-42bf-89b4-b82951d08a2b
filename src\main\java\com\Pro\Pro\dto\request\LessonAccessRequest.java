package com.Pro.Pro.dto.request;

import com.Pro.Pro.model.AccessCode;
import com.Pro.Pro.model.PaymentMethod;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LessonAccessRequest {
    @NotNull
    private String lessonId;


    private String accessCode;  // For code access
    private PaymentMethod paymentMethod;  // For payment access
}