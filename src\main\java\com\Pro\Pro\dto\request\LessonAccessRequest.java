package com.Pro.Pro.dto.request;

import com.Pro.Pro.model.PaymentMethod;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LessonAccessRequest {

    @NotNull
    private String lessonId;


    private String accessCode;  // For code access
    private PaymentMethod paymentMethod;  // For payment access
}