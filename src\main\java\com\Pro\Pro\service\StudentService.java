package com.Pro.Pro.service;

import com.Pro.Pro.dto.payload.LessonPayload;
import com.Pro.Pro.dto.request.InstructorProfileWithCoursesResponse;
import com.Pro.Pro.dto.request.UpdateProfileRequest;
import com.Pro.Pro.dto.response.CourseResponse;
import com.Pro.Pro.dto.response.LessonResponse;
import com.Pro.Pro.dto.response.PaymentResponse;
import com.Pro.Pro.dto.response.UserResponse;
import com.Pro.Pro.model.InstructorProfile;
import java.util.List;

public interface StudentService {
    UserResponse updateProfile(String studentId, UpdateProfileRequest request);
    InstructorProfileWithCoursesResponse getInstructorProfileWithCourses(String instructorId);
    InstructorProfile getInstructorProfile(String instructorId);
    List<CourseResponse> getInstructorCourses(String instructorId);
    LessonPayload getLessonDetails(String lessonId, String studentId);
    PaymentResponse payWithFawry(String lessonId, String studentId);
    List<LessonResponse> getPaidLessons(String studentId);
}