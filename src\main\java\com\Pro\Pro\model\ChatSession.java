package com.Pro.Pro.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

// ChatSession.java
@Entity
@Table(name = "chat_sessions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatSession {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private User user;

    @Column(nullable = false, unique = true)
    private String sessionToken;

    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL)
    @Builder.Default
    @JsonManagedReference("session-messages")
    private Set<ChatMessage> messages = new HashSet<>();

    @Column(nullable = false)
    private Date createdAt;

    @Column(nullable = false)
    private Date updatedAt;

    @Column(name = "last_activity")
    private Date lastActivity;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        this.createdAt = now;
        this.updatedAt = now;
        this.lastActivity = now; // ✅ Set this here
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = new Date();
    }
}
