package com.Pro.Pro.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "student_lessons", indexes = {
        @Index(name = "idx_student_lesson_student", columnList = "student_id"),
        @Index(name = "idx_student_lesson_lesson", columnList = "lesson_id"),
        @Index(name = "idx_student_lesson_payment_method", columnList = "paymentMethod"),
        @Index(name = "idx_student_lesson_completed", columnList = "completed"),
        @Index(name = "idx_student_lesson_expiry", columnList = "access_expiry_date")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
public class StudentLesson extends Auditable {
    @Id
    @GeneratedValue(generator = "nanoid")
    @GenericGenerator(name = "nanoid", strategy = "com.Pro.Pro.NanoIdGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private String id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PaymentMethod paymentMethod;

    private String paymentReference;

    private Date paymentDate;

    private BigDecimal examScore;

    private boolean completed;

    private Date completedDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private User student;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lesson_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude
    private Lesson lesson;

    @Column(name = "access_expiry_date")
    private Date accessExpiryDate;

    @Column
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdated;

    // Add this method to update the timestamp automatically
    @PreUpdate
    @PrePersist
    public void updateTimestamp() {
        this.lastUpdated = new Date();
    }

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private LessonProgressStatus progressStatus = LessonProgressStatus.PURCHASED;
}

