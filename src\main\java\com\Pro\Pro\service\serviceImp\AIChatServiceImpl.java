package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.config.OpenRouterConfig;
import com.Pro.Pro.dto.request.ChatRequest;
import com.Pro.Pro.dto.response.ChatResponse;
import com.Pro.Pro.model.ChatMessage;
import com.Pro.Pro.model.ChatSession;
import com.Pro.Pro.model.User;
import com.Pro.Pro.repository.ChatMessageRepository;
import com.Pro.Pro.repository.ChatSessionRepository;
import com.Pro.Pro.repository.UserRepository;
import com.Pro.Pro.service.AIChatService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AIChatServiceImpl implements AIChatService {

    private final ChatSessionRepository chatSessionRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final UserRepository userRepository;
    @Autowired
    private final RestTemplate restTemplate;
    private final OpenRouterConfig openRouterConfig;

    @Override
    public ChatResponse startNewSession(String userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Create new session with current timestamp
        ChatSession session = ChatSession.builder()
                .user(user)
                .sessionToken(UUID.randomUUID().toString())
                .createdAt(new Date())  // Explicitly set
                .updatedAt(new Date())  // Explicitly set
                .build();

        session = chatSessionRepository.save(session);

        String welcomeMessage = "Hello! I'm your AI learning assistant. How can I help you with your studies today?";

        // Save welcome message
        ChatMessage welcomeMsg = ChatMessage.builder()
                .session(session)
                .messageText(welcomeMessage)
                .isFromUser(false)
                .build();
        chatMessageRepository.save(welcomeMsg);

        return mapMessageToResponse(welcomeMsg);
    }

    @Override
    public ChatResponse sendMessage(ChatRequest request, String userId) {
        try {
            ChatSession session = chatSessionRepository.findBySessionTokenAndUserId(request.getSessionId(), userId)
                    .orElseThrow(() -> new ResourceNotFoundException("Session not found"));

            ChatMessage userMessage = ChatMessage.builder()
                    .session(session)
                    .messageText(request.getMessage())
                    .isFromUser(true)
                    .build();
            chatMessageRepository.save(userMessage);

            List<ChatMessage> history = chatMessageRepository.findBySessionIdOrderByCreatedAtAsc(session.getId());
            String conversationHistory = buildConversationHistory(history);

            String aiResponse = generateAIResponse(request.getMessage(), conversationHistory);

            ChatMessage aiMessage = ChatMessage.builder()
                    .session(session)
                    .messageText(aiResponse)
                    .isFromUser(false)
                    .build();
            chatMessageRepository.save(aiMessage);

            session.setLastActivity(new Date());
            chatSessionRepository.save(session);

            return mapMessageToResponse(aiMessage);
        } catch (Exception e) {
            e.printStackTrace();
            return ChatResponse.builder()
                    .sessionId(request.getSessionId())
                    .message("Error processing your message: " + e.getMessage())
                    .isUser(false)
                    .timestamp(new Date())
                    .build();
        }
    }


    private String generateAIResponse(String userMessage, String conversationHistory) {
        try {
            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + openRouterConfig.getApiKey());
            headers.set("HTTP-Referer", openRouterConfig.getReferer());
            headers.set("X-Title", openRouterConfig.getTitle());

            // Prepare messages list
            List<Map<String, String>> messages = new ArrayList<>();

            // Add conversation history if exists
            if (!conversationHistory.isEmpty()) {
                messages.add(Map.of("role", "system", "content", "You are a helpful assistant. Continue the conversation."));
                // Parse history into proper message format
                String[] historyLines = conversationHistory.split("\n");
                for (String line : historyLines) {
                    if (line.startsWith("User:")) {
                        messages.add(Map.of(
                                "role", "user",
                                "content", line.substring(5).trim()
                        ));
                    } else if (line.startsWith("Assistant:")) {
                        messages.add(Map.of(
                                "role", "assistant",
                                "content", line.substring(10).trim()
                        ));
                    }
                }
            }

            // Add current user message
            messages.add(Map.of("role", "user", "content", userMessage));

            // Prepare request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", openRouterConfig.getModel());
            requestBody.put("messages", messages);
            requestBody.put("temperature", 0.7);
            requestBody.put("max_tokens", 1024);

            // Make the API call
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(
                    openRouterConfig.getApiUrl(),
                    HttpMethod.POST,
                    entity,
                    Map.class
            );

            // Parse response
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
                if (choices != null && !choices.isEmpty()) {
                    Map<String, Object> firstChoice = choices.get(0);
                    Map<String, Object> message = (Map<String, Object>) firstChoice.get("message");
                    return (String) message.get("content");
                }
            }
            return "I encountered an issue generating a response. Please try again.";
        } catch (Exception e) {
            e.printStackTrace();
            return "Sorry, I'm having trouble responding right now. Please try again later.";
        }
    }


    private String buildConversationHistory(List<ChatMessage> messages) {
        StringBuilder history = new StringBuilder();
        for (ChatMessage message : messages) {
            String role = message.isFromUser() ? "User" : "Assistant";
            history.append(role).append(": ").append(message.getMessageText()).append("\n");
        }
        return history.toString();
    }

    @Override
    public List<ChatResponse> getChatHistory(String sessionId, String userId) {
        ChatSession session = chatSessionRepository.findBySessionTokenAndUserId(sessionId, userId)
                .orElseThrow(() -> new ResourceNotFoundException("Session not found"));

        return chatMessageRepository.findBySessionIdOrderByCreatedAtAsc(session.getId()).stream()
                .map(this::mapMessageToResponse)
                .collect(Collectors.toList());
    }

    private ChatResponse mapMessageToResponse(ChatMessage message) {
        return ChatResponse.builder()
                .sessionId(message.getSession().getSessionToken())
                .message(message.getMessageText())
                .isUser(message.isFromUser())
                .timestamp(message.getCreatedAt())
                .build();
    }
}