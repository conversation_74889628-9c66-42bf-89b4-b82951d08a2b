package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.Pro.Pro.dto.request.ExamQuestionRequest;
import com.Pro.Pro.dto.request.ExamRequest;
import com.Pro.Pro.model.QuestionType;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class ExamCreationDebugTest {

    @Test
    public void testExamRequestStructure() {
        System.out.println("🔍 Testing exam request structure...");
        
        // Create exam request with 3 questions
        ExamRequest examRequest = ExamRequest.builder()
                .title("Debug Test Exam")
                .passingScore(new BigDecimal("70.00"))
                .timeLimitMinutes(60)
                .questions(Arrays.asList(
                    // Question 1: Multiple Choice
                    ExamQuestionRequest.builder()
                        .questionText("Question 1: Multiple Choice")
                        .questionType(QuestionType.MULTIPLE_CHOICE)
                        .points(new BigDecimal("20.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("Answer 1A").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("Answer 1B").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("Answer 1C").correct(false).build()
                        ))
                        .build(),
                    
                    // Question 2: Single Choice
                    ExamQuestionRequest.builder()
                        .questionText("Question 2: Single Choice")
                        .questionType(QuestionType.SINGLE_CHOICE)
                        .points(new BigDecimal("15.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("Answer 2A").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("Answer 2B").correct(false).build()
                        ))
                        .build(),
                    
                    // Question 3: True/False
                    ExamQuestionRequest.builder()
                        .questionText("Question 3: True/False")
                        .questionType(QuestionType.TRUE_FALSE)
                        .points(new BigDecimal("10.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("false").correct(false).build()
                        ))
                        .build()
                ))
                .build();

        // Verify the request structure
        assertNotNull(examRequest);
        assertNotNull(examRequest.getQuestions());
        assertEquals(3, examRequest.getQuestions().size());
        
        System.out.println("📊 Exam Request Analysis:");
        System.out.println("Title: " + examRequest.getTitle());
        System.out.println("Questions: " + examRequest.getQuestions().size());
        
        for (int i = 0; i < examRequest.getQuestions().size(); i++) {
            ExamQuestionRequest question = examRequest.getQuestions().get(i);
            System.out.println("\nQuestion " + (i + 1) + ":");
            System.out.println("  Text: " + question.getQuestionText());
            System.out.println("  Type: " + question.getQuestionType());
            System.out.println("  Points: " + question.getPoints());
            System.out.println("  Answers: " + question.getAnswers().size());
            
            for (int j = 0; j < question.getAnswers().size(); j++) {
                ExamAnswerRequest answer = question.getAnswers().get(j);
                System.out.println("    Answer " + (j + 1) + ": " + answer.getAnswerText() + " (Correct: " + answer.isCorrect() + ")");
            }
        }
        
        // Verify each question
        ExamQuestionRequest q1 = examRequest.getQuestions().get(0);
        assertEquals(QuestionType.MULTIPLE_CHOICE, q1.getQuestionType());
        assertEquals(3, q1.getAnswers().size());
        assertEquals("Question 1: Multiple Choice", q1.getQuestionText());
        
        ExamQuestionRequest q2 = examRequest.getQuestions().get(1);
        assertEquals(QuestionType.SINGLE_CHOICE, q2.getQuestionType());
        assertEquals(2, q2.getAnswers().size());
        assertEquals("Question 2: Single Choice", q2.getQuestionText());
        
        ExamQuestionRequest q3 = examRequest.getQuestions().get(2);
        assertEquals(QuestionType.TRUE_FALSE, q3.getQuestionType());
        assertEquals(2, q3.getAnswers().size());
        assertEquals("Question 3: True/False", q3.getQuestionText());
        
        // Calculate total points
        BigDecimal totalPoints = examRequest.getQuestions().stream()
                .map(ExamQuestionRequest::getPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("45.00"), totalPoints);
        
        System.out.println("\n✅ Total Points: " + totalPoints);
        System.out.println("✅ All structure tests passed!");
    }

    @Test
    public void testQuestionValidation() {
        System.out.println("🔍 Testing individual question validation...");
        
        // Test Multiple Choice Question
        ExamQuestionRequest mcQuestion = ExamQuestionRequest.builder()
                .questionText("MC Question")
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(new BigDecimal("10.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct 1").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Correct 2").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong 1").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong 2").correct(false).build()
                ))
                .build();
        
        assertNotNull(mcQuestion);
        assertEquals(4, mcQuestion.getAnswers().size());
        
        long correctCount = mcQuestion.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();
        assertEquals(2, correctCount);
        
        System.out.println("✅ Multiple Choice Question: " + mcQuestion.getAnswers().size() + " answers, " + correctCount + " correct");
        
        // Test Single Choice Question
        ExamQuestionRequest scQuestion = ExamQuestionRequest.builder()
                .questionText("SC Question")
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(new BigDecimal("15.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong 1").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong 2").correct(false).build()
                ))
                .build();
        
        assertNotNull(scQuestion);
        assertEquals(3, scQuestion.getAnswers().size());
        
        correctCount = scQuestion.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();
        assertEquals(1, correctCount);
        
        System.out.println("✅ Single Choice Question: " + scQuestion.getAnswers().size() + " answers, " + correctCount + " correct");
        
        // Test True/False Question
        ExamQuestionRequest tfQuestion = ExamQuestionRequest.builder()
                .questionText("TF Question")
                .questionType(QuestionType.TRUE_FALSE)
                .points(new BigDecimal("5.00"))
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("false").correct(false).build()
                ))
                .build();
        
        assertNotNull(tfQuestion);
        assertEquals(2, tfQuestion.getAnswers().size());
        
        correctCount = tfQuestion.getAnswers().stream()
                .filter(ExamAnswerRequest::isCorrect)
                .count();
        assertEquals(1, correctCount);
        
        System.out.println("✅ True/False Question: " + tfQuestion.getAnswers().size() + " answers, " + correctCount + " correct");
        
        System.out.println("✅ All validation tests passed!");
    }

    @Test
    public void testLargeExamStructure() {
        System.out.println("🔍 Testing large exam structure...");
        
        // Create a larger exam with 10 questions
        ExamRequest largeExam = ExamRequest.builder()
                .title("Large Debug Test Exam")
                .passingScore(new BigDecimal("75.00"))
                .timeLimitMinutes(120)
                .questions(Arrays.asList(
                    createMCQuestion("MC Question 1", new BigDecimal("10.00")),
                    createMCQuestion("MC Question 2", new BigDecimal("10.00")),
                    createMCQuestion("MC Question 3", new BigDecimal("10.00")),
                    createSCQuestion("SC Question 1", new BigDecimal("15.00")),
                    createSCQuestion("SC Question 2", new BigDecimal("15.00")),
                    createSCQuestion("SC Question 3", new BigDecimal("15.00")),
                    createTFQuestion("TF Question 1", new BigDecimal("5.00")),
                    createTFQuestion("TF Question 2", new BigDecimal("5.00")),
                    createTFQuestion("TF Question 3", new BigDecimal("5.00")),
                    createTFQuestion("TF Question 4", new BigDecimal("5.00"))
                ))
                .build();

        assertNotNull(largeExam);
        assertEquals(10, largeExam.getQuestions().size());
        
        // Verify question type distribution
        long mcCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.MULTIPLE_CHOICE)
                .count();
        long scCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.SINGLE_CHOICE)
                .count();
        long tfCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.TRUE_FALSE)
                .count();
        
        assertEquals(3, mcCount);
        assertEquals(3, scCount);
        assertEquals(4, tfCount);
        
        // Calculate total points
        BigDecimal totalPoints = largeExam.getQuestions().stream()
                .map(ExamQuestionRequest::getPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("95.00"), totalPoints);
        
        System.out.println("📊 Large Exam Analysis:");
        System.out.println("Total Questions: " + largeExam.getQuestions().size());
        System.out.println("Multiple Choice: " + mcCount);
        System.out.println("Single Choice: " + scCount);
        System.out.println("True/False: " + tfCount);
        System.out.println("Total Points: " + totalPoints);
        
        System.out.println("✅ Large exam structure test passed!");
    }

    private ExamQuestionRequest createMCQuestion(String text, BigDecimal points) {
        return ExamQuestionRequest.builder()
                .questionText(text)
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(points)
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct A").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Correct B").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong A").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong B").correct(false).build()
                ))
                .build();
    }

    private ExamQuestionRequest createSCQuestion(String text, BigDecimal points) {
        return ExamQuestionRequest.builder()
                .questionText(text)
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(points)
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct answer").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong answer 1").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong answer 2").correct(false).build()
                ))
                .build();
    }

    private ExamQuestionRequest createTFQuestion(String text, BigDecimal points) {
        return ExamQuestionRequest.builder()
                .questionText(text)
                .questionType(QuestionType.TRUE_FALSE)
                .points(points)
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("false").correct(false).build()
                ))
                .build();
    }
}
