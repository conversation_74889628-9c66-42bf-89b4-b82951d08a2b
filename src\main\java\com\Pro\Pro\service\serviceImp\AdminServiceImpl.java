package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.AdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;



@Slf4j
@Service
@RequiredArgsConstructor
public class AdminServiceImpl implements AdminService {

    private final UserRepository userRepository;
    private final InstructorProfileRepository instructorProfileRepository;
    private final CourseRepository courseRepository;
    private final LessonRepository lessonRepository;
    private final PasswordEncoder passwordEncoder;
    private final AccessCodeRepository accessCodeRepository;

    @Override
    public UserResponse createInstructor(CreateInstructorRequest request) {
        try {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new CustomException("Email already in use", HttpStatus.BAD_REQUEST);
            }

            User instructor = User.builder()
                    .email(request.getEmail())
                    .password(passwordEncoder.encode(request.getPassword()))
                    .username(request.getUsername())
                    .fullname(request.getFullname())
                    .phoneNumber(request.getPhoneNumber())
                    .nationalId(request.getNationalId())
                    .government(request.getGovernment())
                    .role(Role.INSTRUCTOR)
                    .build();

            instructor = userRepository.save(instructor);

            InstructorProfile profile = InstructorProfile.builder()
                    .user(instructor)
                    .bio(request.getBio())
                    .photoUrl(request.getPhotoUrl())
                    .build();

            instructorProfileRepository.save(profile);

            return mapUserToResponse(instructor);
        } catch (CustomException e) {
            log.error("Create instructor failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to create instructor", e);
            throw new CustomException("Failed to create instructor", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public UserResponse createAssistant(CreateAssistantRequest request, String instructorId) {
        try {
            User instructor = userRepository.findByIdAndRole(instructorId, Role.INSTRUCTOR)
                    .orElseThrow(() -> new ResourceNotFoundException("Instructor not found with ID: " + instructorId));

            if (userRepository.existsByEmail(request.getEmail())) {
                throw new CustomException("Email already in use", HttpStatus.BAD_REQUEST);
            }

            User assistant = User.builder()
                    .email(request.getEmail())
                    .password(passwordEncoder.encode(request.getPassword()))
                    .username(request.getUsername())
                    .fullname(request.getFullname())
                    .phoneNumber(request.getPhoneNumber())
                    .nationalId(request.getNationalId())
                    .government(request.getGovernment())
                    .role(Role.ASSISTANT)
                    .instructor(instructor)
                    .build();

            assistant = userRepository.save(assistant);
            return mapUserToResponse(assistant);
        } catch (ResourceNotFoundException | CustomException e) {
            log.error("Create assistant failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to create assistant", e);
            throw new CustomException("Failed to create assistant", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public CourseResponse createCourse(CourseRequest request, String instructorId) {
        try {
            User instructor = userRepository.findByIdAndRole(instructorId, Role.INSTRUCTOR)
                    .orElseThrow(() -> new ResourceNotFoundException("Instructor not found with ID: " + instructorId));

            Course course = Course.builder()
                    .instructor(instructor)
                    .name(request.getName())
                    .description(request.getDescription())
                    .photoUrl(request.getPhotoUrl())
                    .build();

            course = courseRepository.save(course);
            return mapCourseToResponse(course);
        } catch (ResourceNotFoundException e) {
            log.error("Create course failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to create course", e);
            throw new CustomException("Failed to create course", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    public void deleteUser(String userId) {
        try {
            if (!userRepository.existsById(userId)) {
                throw new ResourceNotFoundException("User not found with ID: " + userId);
            }
            userRepository.deleteById(userId);
        } catch (ResourceNotFoundException e) {
            log.error("Delete user failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete user: {}", userId, e);
            throw new CustomException("Failed to delete user", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    public void deleteCourse(String courseId) {
        try {
            if (!courseRepository.existsById(courseId)) {
                throw new ResourceNotFoundException("Course not found with ID: " + courseId);
            }
            courseRepository.deleteById(courseId);
        } catch (ResourceNotFoundException e) {
            log.error("Delete course failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete course: {}", courseId, e);
            throw new CustomException("Failed to delete course", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    public void deleteLesson(String lessonId) {
        try {
            if (!lessonRepository.existsById(lessonId)) {
                throw new ResourceNotFoundException("Lesson not found with ID: " + lessonId);
            }
            lessonRepository.deleteById(lessonId);
        } catch (ResourceNotFoundException e) {
            log.error("Delete lesson failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete lesson: {}", lessonId, e);
            throw new CustomException("Failed to delete lesson", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    public CourseResponse updateCourse(String courseId, CourseRequest request) {
        try {
            Course course = courseRepository.findById(courseId)
                    .orElseThrow(() -> new ResourceNotFoundException("Course not found with ID: " + courseId));

            Optional.ofNullable(request.getName()).ifPresent(course::setName);
            Optional.ofNullable(request.getDescription()).ifPresent(course::setDescription);
            Optional.ofNullable(request.getPhotoUrl()).ifPresent(course::setPhotoUrl);

            course = courseRepository.save(course);
            return mapCourseToResponse(course);
        } catch (ResourceNotFoundException e) {
            log.error("Update course failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to update course: {}", courseId, e);
            throw new CustomException("Failed to update course", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('ADMIN')")
    public Page<AccessCodeResponse> getAllAccessCodes(Pageable pageable) {
        try {
            return accessCodeRepository.findAll(pageable)
                    .map(this::mapAccessCodeToResponse);
        } catch (Exception e) {
            log.error("Failed to get all access codes", e);
            throw new CustomException("Failed to retrieve access codes", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    public LessonResponse updateLesson(String lessonId, LessonRequest request) {
        try {
            Lesson lesson = lessonRepository.findById(lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("Lesson not found with ID: " + lessonId));

            Optional.ofNullable(request.getName()).ifPresent(lesson::setName);
            Optional.ofNullable(request.getDescription()).ifPresent(lesson::setDescription);
            Optional.ofNullable(request.getPhotoUrl()).ifPresent(lesson::setPhotoUrl);
            Optional.ofNullable(request.getPrice()).ifPresent(lesson::setPrice);
            Optional.ofNullable(request.getVideoUrl()).ifPresent(lesson::setVideoUrl);

            lesson = lessonRepository.save(lesson);
            return mapLessonToResponse(lesson);
        } catch (ResourceNotFoundException e) {
            log.error("Update lesson failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to update lesson: {}", lessonId, e);
            throw new CustomException("Failed to update lesson", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @Transactional
    public UserResponse updateInstructorProfile(String instructorId, UpdateProfileRequest request) {
        try {
            User instructor = userRepository.findByIdAndRole(instructorId, Role.INSTRUCTOR)
                    .orElseThrow(() -> new ResourceNotFoundException("Instructor not found with ID: " + instructorId));

            Optional.ofNullable(request.getPhoneNumber()).ifPresent(instructor::setPhoneNumber);
            Optional.ofNullable(request.getAvatarUrl()).ifPresent(instructor::setAvatarUrl);
            Optional.ofNullable(request.getUsername()).ifPresent(instructor::setUsername);
            instructor = userRepository.save(instructor);

            Optional<InstructorProfile> profileOpt = instructorProfileRepository.findByUserId(instructorId);
            if (profileOpt.isPresent()) {
                InstructorProfile profile = profileOpt.get();
                Optional.ofNullable(request.getBio()).ifPresent(profile::setBio);
                Optional.ofNullable(request.getPhotoUrl()).ifPresent(profile::setPhotoUrl);
                instructorProfileRepository.save(profile);
            }

            return mapUserToResponse(instructor);
        } catch (ResourceNotFoundException e) {
            log.error("Update instructor profile failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to update instructor profile: {}", instructorId, e);
            throw new CustomException("Failed to update profile", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public LessonResponse createLesson(LessonRequest request, String courseId) {
        try {
            Course course = courseRepository.findById(courseId)
                    .orElseThrow(() -> new ResourceNotFoundException("Course not found with ID: " + courseId));

            Lesson lesson = Lesson.builder()
                    .course(course)
                    .name(request.getName())
                    .description(request.getDescription())
                    .photoUrl(request.getPhotoUrl())
                    .price(request.getPrice())
                    .videoUrl(request.getVideoUrl())
                    .build();

            lesson = lessonRepository.save(lesson);
            return mapLessonToResponse(lesson);
        } catch (ResourceNotFoundException e) {
            log.error("Create lesson failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to create lesson", e);
            throw new CustomException("Failed to create lesson", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional
    public List<AccessCode> generateAccessCodes(String lessonId, int count, String adminId) {
        try {
            Lesson lesson = lessonRepository.findById(lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("Lesson not found with ID: " + lessonId));

            User admin = userRepository.findById(adminId)
                    .orElseThrow(() -> new ResourceNotFoundException("Admin not found with ID: " + adminId));

            List<AccessCode> codes = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                AccessCode code = AccessCode.builder()
                        .code(generateRandomCode())
                        .lesson(lesson)
                        .creator(admin)
                        .build();
                codes.add(accessCodeRepository.save(code));
            }
            return codes;
        } catch (ResourceNotFoundException e) {
            log.error("Generate access codes failed: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Failed to generate access codes", e);
            throw new CustomException("Failed to generate access codes", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public List<UserResponse> getAllStudents() {
        try {
            return userRepository.findByRole(Role.STUDENT).stream()
                    .map(this::mapUserToResponse)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get all students", e);
            throw new CustomException("Failed to retrieve students", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public List<User> searchStudentsByUsername(String usernamePart) {
        try {
            return userRepository.findByUsernameContainingIgnoreCase(usernamePart);
        } catch (Exception e) {
            throw new RuntimeException("Error searching students with name part: " + usernamePart, e);
        }
    }

    private String generateRandomCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(8);
        for (int i = 0; i < 8; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    private UserResponse mapUserToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .username(user.getUsername())
                .fullname(user.getFullname())
                .phoneNumber(user.getPhoneNumber())
                .parentPhoneNumber(user.getParentPhoneNumber())
                .dateOfBirth(user.getDateOfBirth())
                .nationalId(user.getNationalId())
                .government(user.getGovernment())
                .avatarUrl(user.getAvatarUrl())
                .role(user.getRole().name())
                .createdAt(user.getCreatedAt())
                .build();
    }

    private CourseResponse mapCourseToResponse(Course course) {
        InstructorProfile profile = instructorProfileRepository.findByUserId(course.getInstructor().getId()).orElse(null);

        return CourseResponse.builder()
                .id(course.getId())
                .name(course.getName())
                .description(course.getDescription())
                .photoUrl(course.getPhotoUrl())
                .instructorId(course.getInstructor().getId())
                .instructorName(course.getInstructor().getFullname())
                .instructorBio(profile != null ? profile.getBio() : null)
                .instructorPhoto(profile != null ? profile.getPhotoUrl() : null)
                .createdAt(course.getCreatedAt())
                .lessonCount(course.getLessons() != null ? course.getLessons().size() : 0)
                .build();
    }

    private LessonResponse mapLessonToResponse(Lesson lesson) {
        return LessonResponse.builder()
                .id(lesson.getId())
                .courseId(lesson.getCourse().getId())
                .name(lesson.getName())
                .description(lesson.getDescription())
                .photoUrl(lesson.getPhotoUrl())
                .price(lesson.getPrice())
                .build();
    }

    private AccessCodeResponse mapAccessCodeToResponse(AccessCode accessCode) {
        return AccessCodeResponse.builder()
                .id(accessCode.getId())
                .code(accessCode.getCode())
                .used(accessCode.isUsed())
                .usedAt(accessCode.getUsedAt())
                .lessonId(accessCode.getLesson().getId())
                .lessonName(accessCode.getLesson().getName())
                .creatorId(accessCode.getCreator().getId())
                .creatorName(accessCode.getCreator().getUsername())
                .createdAt(accessCode.getCreatedAt())
                .expiryDate(accessCode.getExpiryDate())
                .build();
    }
}