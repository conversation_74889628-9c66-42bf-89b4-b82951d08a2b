package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.Pro.Pro.dto.request.ExamQuestionRequest;
import com.Pro.Pro.dto.request.ExamRequest;
import com.Pro.Pro.dto.response.ExamResponse;
import com.Pro.Pro.model.QuestionType;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class ExamCreationVerificationTest {

    @Test
    public void testExamCreationWithAllQuestionsAndAnswers() {
        // Create the exact same exam structure as in the Postman test
        ExamRequest examRequest = ExamRequest.builder()
                .title("Comprehensive Programming Assessment")
                .passingScore(new BigDecimal("70.00"))
                .timeLimitMinutes(90)
                .questions(Arrays.asList(
                    // Multiple choice question with 3 answers
                    ExamQuestionRequest.builder()
                        .questionText("Which are valid Java data types?")
                        .questionType(QuestionType.MULTIPLE_CHOICE)
                        .points(new BigDecimal("20.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("int").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("String").correct(false).build(),
                            ExamAnswerRequest.builder().answerText("boolean").correct(true).build()
                        ))
                        .build(),
                    
                    // Single choice question with 2 answers
                    ExamQuestionRequest.builder()
                        .questionText("What is the default access modifier?")
                        .questionType(QuestionType.SINGLE_CHOICE)
                        .points(new BigDecimal("15.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("package-private").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("public").correct(false).build()
                        ))
                        .build(),
                    
                    // True/False question with 2 answers
                    ExamQuestionRequest.builder()
                        .questionText("Java is case-sensitive.")
                        .questionType(QuestionType.TRUE_FALSE)
                        .points(new BigDecimal("10.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("false").correct(false).build()
                        ))
                        .build()
                ))
                .build();

        // Verify the request structure
        assertNotNull(examRequest);
        assertEquals(3, examRequest.getQuestions().size());
        
        // Verify first question (Multiple Choice)
        ExamQuestionRequest firstQuestion = examRequest.getQuestions().get(0);
        assertEquals(QuestionType.MULTIPLE_CHOICE, firstQuestion.getQuestionType());
        assertEquals(3, firstQuestion.getAnswers().size());
        assertEquals("Which are valid Java data types?", firstQuestion.getQuestionText());
        
        // Verify answers for first question
        assertEquals("int", firstQuestion.getAnswers().get(0).getAnswerText());
        assertTrue(firstQuestion.getAnswers().get(0).isCorrect());
        assertEquals("String", firstQuestion.getAnswers().get(1).getAnswerText());
        assertFalse(firstQuestion.getAnswers().get(1).isCorrect());
        assertEquals("boolean", firstQuestion.getAnswers().get(2).getAnswerText());
        assertTrue(firstQuestion.getAnswers().get(2).isCorrect());
        
        // Verify second question (Single Choice)
        ExamQuestionRequest secondQuestion = examRequest.getQuestions().get(1);
        assertEquals(QuestionType.SINGLE_CHOICE, secondQuestion.getQuestionType());
        assertEquals(2, secondQuestion.getAnswers().size());
        assertEquals("What is the default access modifier?", secondQuestion.getQuestionText());
        
        // Verify answers for second question
        assertEquals("package-private", secondQuestion.getAnswers().get(0).getAnswerText());
        assertTrue(secondQuestion.getAnswers().get(0).isCorrect());
        assertEquals("public", secondQuestion.getAnswers().get(1).getAnswerText());
        assertFalse(secondQuestion.getAnswers().get(1).isCorrect());
        
        // Verify third question (True/False)
        ExamQuestionRequest thirdQuestion = examRequest.getQuestions().get(2);
        assertEquals(QuestionType.TRUE_FALSE, thirdQuestion.getQuestionType());
        assertEquals(2, thirdQuestion.getAnswers().size());
        assertEquals("Java is case-sensitive.", thirdQuestion.getQuestionText());
        
        // Verify answers for third question
        assertEquals("true", thirdQuestion.getAnswers().get(0).getAnswerText());
        assertTrue(thirdQuestion.getAnswers().get(0).isCorrect());
        assertEquals("false", thirdQuestion.getAnswers().get(1).getAnswerText());
        assertFalse(thirdQuestion.getAnswers().get(1).isCorrect());
        
        // Verify total points calculation
        BigDecimal totalPoints = examRequest.getQuestions().stream()
                .map(ExamQuestionRequest::getPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("45.00"), totalPoints);
        
        System.out.println("✅ Exam request structure verification passed!");
        System.out.println("📊 Total questions: " + examRequest.getQuestions().size());
        System.out.println("💯 Total points: " + totalPoints);
        System.out.println("🎯 Passing score: " + examRequest.getPassingScore());
        System.out.println("⏱️ Time limit: " + examRequest.getTimeLimitMinutes() + " minutes");
        
        // Print question details
        for (int i = 0; i < examRequest.getQuestions().size(); i++) {
            ExamQuestionRequest question = examRequest.getQuestions().get(i);
            System.out.println("\n📝 Question " + (i + 1) + ":");
            System.out.println("   Type: " + question.getQuestionType());
            System.out.println("   Text: " + question.getQuestionText());
            System.out.println("   Points: " + question.getPoints());
            System.out.println("   Answers: " + question.getAnswers().size());
            
            for (int j = 0; j < question.getAnswers().size(); j++) {
                ExamAnswerRequest answer = question.getAnswers().get(j);
                System.out.println("     " + (j + 1) + ". " + answer.getAnswerText() + 
                                 " (" + (answer.isCorrect() ? "✅ Correct" : "❌ Wrong") + ")");
            }
        }
    }

    @Test
    public void testLargeExamStructure() {
        // Test with a larger exam to ensure scalability
        ExamRequest largeExam = ExamRequest.builder()
                .title("Large Mixed Type Exam")
                .passingScore(new BigDecimal("75.00"))
                .timeLimitMinutes(120)
                .questions(Arrays.asList(
                    // 3 Multiple choice questions
                    createMultipleChoiceQuestion("MC Question 1", new BigDecimal("10.00")),
                    createMultipleChoiceQuestion("MC Question 2", new BigDecimal("10.00")),
                    createMultipleChoiceQuestion("MC Question 3", new BigDecimal("10.00")),
                    
                    // 3 Single choice questions
                    createSingleChoiceQuestion("SC Question 1", new BigDecimal("15.00")),
                    createSingleChoiceQuestion("SC Question 2", new BigDecimal("15.00")),
                    createSingleChoiceQuestion("SC Question 3", new BigDecimal("15.00")),
                    
                    // 2 True/False questions
                    createTrueFalseQuestion("TF Question 1", new BigDecimal("5.00")),
                    createTrueFalseQuestion("TF Question 2", new BigDecimal("5.00"))
                ))
                .build();

        assertNotNull(largeExam);
        assertEquals(8, largeExam.getQuestions().size());
        
        // Verify question type distribution
        long mcCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.MULTIPLE_CHOICE)
                .count();
        long scCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.SINGLE_CHOICE)
                .count();
        long tfCount = largeExam.getQuestions().stream()
                .filter(q -> q.getQuestionType() == QuestionType.TRUE_FALSE)
                .count();
        
        assertEquals(3, mcCount);
        assertEquals(3, scCount);
        assertEquals(2, tfCount);
        
        // Verify total points
        BigDecimal totalPoints = largeExam.getQuestions().stream()
                .map(ExamQuestionRequest::getPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        assertEquals(new BigDecimal("85.00"), totalPoints);
        
        System.out.println("✅ Large exam structure verification passed!");
        System.out.println("📊 Total questions: " + largeExam.getQuestions().size());
        System.out.println("💯 Total points: " + totalPoints);
    }

    private ExamQuestionRequest createMultipleChoiceQuestion(String text, BigDecimal points) {
        return ExamQuestionRequest.builder()
                .questionText(text)
                .questionType(QuestionType.MULTIPLE_CHOICE)
                .points(points)
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct A").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Correct B").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong A").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong B").correct(false).build()
                ))
                .build();
    }

    private ExamQuestionRequest createSingleChoiceQuestion(String text, BigDecimal points) {
        return ExamQuestionRequest.builder()
                .questionText(text)
                .questionType(QuestionType.SINGLE_CHOICE)
                .points(points)
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("Correct answer").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("Wrong answer 1").correct(false).build(),
                    ExamAnswerRequest.builder().answerText("Wrong answer 2").correct(false).build()
                ))
                .build();
    }

    private ExamQuestionRequest createTrueFalseQuestion(String text, BigDecimal points) {
        return ExamQuestionRequest.builder()
                .questionText(text)
                .questionType(QuestionType.TRUE_FALSE)
                .points(points)
                .answers(Arrays.asList(
                    ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                    ExamAnswerRequest.builder().answerText("false").correct(false).build()
                ))
                .build();
    }
}
