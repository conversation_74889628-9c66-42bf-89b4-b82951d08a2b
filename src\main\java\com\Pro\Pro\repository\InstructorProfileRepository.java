package com.Pro.Pro.repository;

import com.Pro.Pro.model.InstructorProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface InstructorProfileRepository extends JpaRepository<InstructorProfile, String> {
    Optional<InstructorProfile> findByUserId(String userId);
}