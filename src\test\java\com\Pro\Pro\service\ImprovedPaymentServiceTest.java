package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.LessonAccessRequest;
import com.Pro.Pro.dto.response.PaymentResponse;
import com.Pro.Pro.exception.BadRequestException;
import com.Pro.Pro.exception.ConflictException;
import com.Pro.Pro.exception.ResourceNotFoundException;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.serviceImp.PaymentServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ImprovedPaymentServiceTest {

    @Autowired
    private PaymentServiceImpl paymentService;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private StudentLessonRepository studentLessonRepository;

    @Autowired
    private FawryPaymentRepository fawryPaymentRepository;

    @Autowired
    private AccessCodeRepository accessCodeRepository;

    private User instructor;
    private User student;
    private Course course;
    private Lesson lesson;

    @BeforeEach
    void setUp() {
        // Create instructor
        instructor = User.builder()
                .username("instructor")
                .email("<EMAIL>")
                .password("password")
                .role(Role.INSTRUCTOR)
                .fullname("Test Instructor")
                .build();
        instructor = userRepository.save(instructor);

        // Create student
        student = User.builder()
                .username("student")
                .email("<EMAIL>")
                .password("password")
                .role(Role.STUDENT)
                .fullname("Test Student")
                .build();
        student = userRepository.save(student);

        // Create course
        course = Course.builder()
                .name("Test Course")
                .description("Test Description")
                .instructor(instructor)
                .build();
        course = courseRepository.save(course);

        // Create lesson
        lesson = Lesson.builder()
                .name("Test Lesson")
                .description("Test Lesson Description")
                .price(new BigDecimal("100.00"))
                .videoUrl("http://test.com/video")
                .course(course)
                .build();
        lesson = lessonRepository.save(lesson);
    }

    @Test
    public void testValidationWithNullRequest() {
        System.out.println("🧪 اختبار التحقق من الطلب الفارغ...");
        
        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(null, student.getId());
        });

        assertTrue(exception.getMessage().contains("طلب الوصول للدرس مطلوب"));
        System.out.println("✅ تم اكتشاف الطلب الفارغ: " + exception.getMessage());
    }

    @Test
    public void testValidationWithEmptyStudentId() {
        System.out.println("🧪 اختبار التحقق من معرف الطالب الفارغ...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId(lesson.getId())
                .paymentMethod(PaymentMethod.FAWRY)
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(request, "");
        });

        assertTrue(exception.getMessage().contains("معرف الطالب مطلوب"));
        System.out.println("✅ تم اكتشاف معرف الطالب الفارغ: " + exception.getMessage());
    }

    @Test
    public void testValidationWithEmptyLessonId() {
        System.out.println("🧪 اختبار التحقق من معرف الدرس الفارغ...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId("")
                .paymentMethod(PaymentMethod.FAWRY)
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(request, student.getId());
        });

        assertTrue(exception.getMessage().contains("معرف الدرس مطلوب"));
        System.out.println("✅ تم اكتشاف معرف الدرس الفارغ: " + exception.getMessage());
    }

    @Test
    public void testValidationWithBothAccessCodeAndPayment() {
        System.out.println("🧪 اختبار التحقق من استخدام كود الوصول والدفع معاً...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId(lesson.getId())
                .accessCode("TEST123")
                .paymentMethod(PaymentMethod.FAWRY)
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(request, student.getId());
        });

        assertTrue(exception.getMessage().contains("لا يمكن استخدام كود الوصول وطريقة الدفع معاً"));
        System.out.println("✅ تم اكتشاف الاستخدام المزدوج: " + exception.getMessage());
    }

    @Test
    public void testValidationWithNeitherAccessCodeNorPayment() {
        System.out.println("🧪 اختبار التحقق من عدم وجود كود وصول أو طريقة دفع...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId(lesson.getId())
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(request, student.getId());
        });

        assertTrue(exception.getMessage().contains("يجب توفير إما كود الوصول أو طريقة الدفع"));
        System.out.println("✅ تم اكتشاف عدم وجود طريقة وصول: " + exception.getMessage());
    }

    @Test
    public void testValidationWithInvalidAccessCode() {
        System.out.println("🧪 اختبار التحقق من كود الوصول غير الصحيح...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId(lesson.getId())
                .accessCode("123") // Too short
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(request, student.getId());
        });

        assertTrue(exception.getMessage().contains("كود الوصول يجب أن يكون بين 6 و 12 حرف"));
        System.out.println("✅ تم اكتشاف كود الوصول القصير: " + exception.getMessage());
    }

    @Test
    public void testValidationWithInvalidAccessCodeCharacters() {
        System.out.println("🧪 اختبار التحقق من أحرف كود الوصول غير الصحيحة...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId(lesson.getId())
                .accessCode("TEST@123") // Contains invalid character
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(request, student.getId());
        });

        assertTrue(exception.getMessage().contains("كود الوصول يجب أن يحتوي على أحرف وأرقام إنجليزية فقط"));
        System.out.println("✅ تم اكتشاف أحرف كود الوصول غير الصحيحة: " + exception.getMessage());
    }

    @Test
    public void testValidationWithNonExistentLesson() {
        System.out.println("🧪 اختبار التحقق من الدرس غير الموجود...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId("nonexistent-lesson-id")
                .paymentMethod(PaymentMethod.FAWRY)
                .build();

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            paymentService.grantLessonAccess(request, student.getId());
        });

        assertTrue(exception.getMessage().contains("الدرس غير موجود"));
        System.out.println("✅ تم اكتشاف الدرس غير الموجود: " + exception.getMessage());
    }

    @Test
    public void testValidationWithNonExistentStudent() {
        System.out.println("🧪 اختبار التحقق من الطالب غير الموجود...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId(lesson.getId())
                .paymentMethod(PaymentMethod.FAWRY)
                .build();

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            paymentService.grantLessonAccess(request, "nonexistent-student-id");
        });

        assertTrue(exception.getMessage().contains("الطالب غير موجود"));
        System.out.println("✅ تم اكتشاف الطالب غير الموجود: " + exception.getMessage());
    }

    @Test
    public void testValidationWithNonStudentUser() {
        System.out.println("🧪 اختبار التحقق من المستخدم غير الطالب...");
        
        LessonAccessRequest request = LessonAccessRequest.builder()
                .lessonId(lesson.getId())
                .paymentMethod(PaymentMethod.FAWRY)
                .build();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.grantLessonAccess(request, instructor.getId());
        });

        assertTrue(exception.getMessage().contains("المستخدم ليس طالباً"));
        System.out.println("✅ تم اكتشاف المستخدم غير الطالب: " + exception.getMessage());
    }

    @Test
    public void testHasValidAccessWithValidIds() {
        System.out.println("🧪 اختبار التحقق من الوصول الصالح...");
        
        // Create valid access
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 7); // 7 days from now
        Date futureDate = calendar.getTime();

        StudentLesson access = StudentLesson.builder()
                .student(student)
                .lesson(lesson)
                .paymentMethod(PaymentMethod.FAWRY)
                .paymentReference("TEST-REF-123")
                .paymentDate(new Date())
                .accessExpiryDate(futureDate)
                .completed(false)
                .progressStatus(LessonProgressStatus.PURCHASED)
                .build();
        studentLessonRepository.save(access);

        boolean hasAccess = paymentService.hasValidAccess(student.getId(), lesson.getId());
        assertTrue(hasAccess);
        
        System.out.println("✅ تم التحقق من الوصول الصالح بنجاح");
    }

    @Test
    public void testHasValidAccessWithExpiredAccess() {
        System.out.println("🧪 اختبار التحقق من الوصول المنتهي الصلاحية...");
        
        // Create expired access
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1); // 1 day ago
        Date pastDate = calendar.getTime();

        StudentLesson access = StudentLesson.builder()
                .student(student)
                .lesson(lesson)
                .paymentMethod(PaymentMethod.FAWRY)
                .paymentReference("TEST-REF-123")
                .paymentDate(new Date())
                .accessExpiryDate(pastDate)
                .completed(false)
                .progressStatus(LessonProgressStatus.PURCHASED)
                .build();
        studentLessonRepository.save(access);

        boolean hasAccess = paymentService.hasValidAccess(student.getId(), lesson.getId());
        assertFalse(hasAccess);
        
        System.out.println("✅ تم التحقق من الوصول المنتهي الصلاحية بنجاح");
    }

    @Test
    public void testHasValidAccessWithInvalidStudentId() {
        System.out.println("🧪 اختبار التحقق من الوصول بمعرف طالب غير صحيح...");
        
        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.hasValidAccess("", lesson.getId());
        });

        assertTrue(exception.getMessage().contains("معرف الطالب مطلوب"));
        System.out.println("✅ تم اكتشاف معرف الطالب غير الصحيح: " + exception.getMessage());
    }

    @Test
    public void testHasValidAccessWithInvalidLessonId() {
        System.out.println("🧪 اختبار التحقق من الوصول بمعرف درس غير صحيح...");
        
        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            paymentService.hasValidAccess(student.getId(), "");
        });

        assertTrue(exception.getMessage().contains("معرف الدرس مطلوب"));
        System.out.println("✅ تم اكتشاف معرف الدرس غير الصحيح: " + exception.getMessage());
    }
}
