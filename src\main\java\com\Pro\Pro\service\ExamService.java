package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.*;
import com.Pro.Pro.dto.response.*;
import java.util.Map;


public interface ExamService {
    ExamResponse createExam(ExamRequest request, String lessonId);
    ExamResponse getExam(String examId);
    ExamResponse submitExam(String examId, Map<String, String> answers, String studentId);
    void deleteExam(String examId);
    ExamResponse updateExam(ExamUpdateRequest request);
    ExamResultResponse getExamResults(String examId);

}