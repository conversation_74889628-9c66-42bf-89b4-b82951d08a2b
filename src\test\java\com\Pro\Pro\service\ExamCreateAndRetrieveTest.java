package com.Pro.Pro.service;

import com.Pro.Pro.dto.request.ExamAnswerRequest;
import com.Pro.Pro.dto.request.ExamQuestionRequest;
import com.Pro.Pro.dto.request.ExamRequest;
import com.Pro.Pro.dto.response.ExamResponse;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.security.UserDetailsImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ExamCreateAndRetrieveTest {

    @Autowired
    private ExamService examService;

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CourseRepository courseRepository;

    private User instructor;
    private Course course;
    private Lesson lesson;

    @BeforeEach
    void setUp() {
        // Create instructor first
        instructor = User.builder()
                .username("instructor")
                .email("<EMAIL>")
                .password("password")
                .role(Role.INSTRUCTOR)
                .build();
        instructor = userRepository.save(instructor);

        // Set up security context for instructor using UserDetailsImpl
        UserDetailsImpl instructorDetails = UserDetailsImpl.build(instructor);
        UsernamePasswordAuthenticationToken instructorAuth = new UsernamePasswordAuthenticationToken(
                instructorDetails, "password", instructorDetails.getAuthorities()
        );
        SecurityContextHolder.getContext().setAuthentication(instructorAuth);

        // Create course
        course = Course.builder()
                .name("Test Course")
                .description("Test Description")
                .instructor(instructor)
                .build();
        course = courseRepository.save(course);

        // Create lesson
        lesson = Lesson.builder()
                .name("Test Lesson")
                .description("Test Lesson Description")
                .price(new BigDecimal("100.00"))
                .videoUrl("http://test.com/video")
                .course(course)
                .build();
        lesson = lessonRepository.save(lesson);
    }

    @Test
    public void testCreateAndImmediatelyRetrieveExam() {
        System.out.println("🧪 Testing create and immediate retrieve...");
        
        // Create exam request with 3 questions
        ExamRequest examRequest = ExamRequest.builder()
                .title("Test Mixed Exam")
                .passingScore(new BigDecimal("70.00"))
                .timeLimitMinutes(60)
                .questions(Arrays.asList(
                    // Question 1: Multiple Choice
                    ExamQuestionRequest.builder()
                        .questionText("Which are programming languages?")
                        .questionType(QuestionType.MULTIPLE_CHOICE)
                        .points(new BigDecimal("20.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("Java").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("Python").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("HTML").correct(false).build()
                        ))
                        .build(),
                    
                    // Question 2: Single Choice
                    ExamQuestionRequest.builder()
                        .questionText("What is 2+2?")
                        .questionType(QuestionType.SINGLE_CHOICE)
                        .points(new BigDecimal("15.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("4").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("5").correct(false).build()
                        ))
                        .build(),
                    
                    // Question 3: True/False
                    ExamQuestionRequest.builder()
                        .questionText("The sky is blue.")
                        .questionType(QuestionType.TRUE_FALSE)
                        .points(new BigDecimal("10.00"))
                        .answers(Arrays.asList(
                            ExamAnswerRequest.builder().answerText("true").correct(true).build(),
                            ExamAnswerRequest.builder().answerText("false").correct(false).build()
                        ))
                        .build()
                ))
                .build();

        // Create the exam
        System.out.println("📝 Creating exam...");
        ExamResponse createdExam = examService.createExam(examRequest, lesson.getId());
        
        assertNotNull(createdExam);
        assertNotNull(createdExam.getId());
        assertEquals("Test Mixed Exam", createdExam.getTitle());
        
        System.out.println("✅ Exam created with ID: " + createdExam.getId());
        System.out.println("📊 Questions in response: " + createdExam.getQuestions().size());
        
        // Immediately retrieve the exam using the service
        System.out.println("🔍 Retrieving exam using service...");
        ExamResponse retrievedExam = examService.getExam(createdExam.getId());
        
        assertNotNull(retrievedExam);
        assertEquals(createdExam.getId(), retrievedExam.getId());
        
        System.out.println("📊 Questions in retrieved exam: " + retrievedExam.getQuestions().size());
        
        // Also test direct repository access
        System.out.println("🔍 Testing direct repository access...");
        Optional<Exam> directExam = examRepository.findById(createdExam.getId());
        assertTrue(directExam.isPresent());
        
        // Force load questions
        Exam exam = directExam.get();
        exam.getQuestions().size(); // Trigger lazy loading
        
        System.out.println("📊 Questions in direct repository access: " + exam.getQuestions().size());
        
        // Print detailed information
        System.out.println("\n📋 Detailed Exam Information:");
        System.out.println("Exam ID: " + exam.getId());
        System.out.println("Title: " + exam.getTitle());
        System.out.println("Questions: " + exam.getQuestions().size());
        
        int questionNum = 1;
        for (ExamQuestion question : exam.getQuestions()) {
            System.out.println("  Question " + questionNum + ": " + question.getQuestionText());
            System.out.println("    Type: " + question.getQuestionType());
            System.out.println("    Points: " + question.getPoints());
            
            // Force load answers
            question.getAnswers().size();
            System.out.println("    Answers: " + question.getAnswers().size());
            
            int answerNum = 1;
            for (ExamAnswer answer : question.getAnswers()) {
                System.out.println("      Answer " + answerNum + ": " + answer.getAnswerText() + " (Correct: " + answer.isCorrect() + ")");
                answerNum++;
            }
            questionNum++;
        }
        
        // Verify the data
        assertEquals(3, exam.getQuestions().size(), "Should have 3 questions");
        assertEquals(3, retrievedExam.getQuestions().size(), "Retrieved exam should have 3 questions");
        
        // Verify each question has the correct number of answers
        for (ExamQuestion question : exam.getQuestions()) {
            assertTrue(question.getAnswers().size() >= 2, "Each question should have at least 2 answers");
            
            switch (question.getQuestionType()) {
                case MULTIPLE_CHOICE:
                    assertEquals(3, question.getAnswers().size(), "Multiple choice should have 3 answers");
                    break;
                case SINGLE_CHOICE:
                    assertEquals(2, question.getAnswers().size(), "Single choice should have 2 answers");
                    break;
                case TRUE_FALSE:
                    assertEquals(2, question.getAnswers().size(), "True/false should have 2 answers");
                    break;
            }
        }
        
        System.out.println("✅ All tests passed!");
    }
}
