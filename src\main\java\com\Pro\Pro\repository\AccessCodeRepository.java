package com.Pro.Pro.repository;

import com.Pro.Pro.model.AccessCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AccessCodeRepository extends JpaRepository<AccessCode, Long> {
    Optional<AccessCode> findByCode(String code);
    List<AccessCode> findByLessonId(String lessonId);

    List<AccessCode> findByLessonCourseInstructorId(String instructorId);
    @Query("SELECT ac FROM AccessCode ac WHERE ac.code = :code AND ac.used = false AND ac.expiryDate > CURRENT_DATE")
    Optional<AccessCode> findValidByCode(@Param("code") String code);

    Optional<AccessCode> findByCodeAndLesson_Id(String code, String lessonId);
    boolean existsByCodeAndUsedFalse(String code);

    @Query(value = """
        SELECT ac.* FROM access_codes ac 
        WHERE ac.lesson_id = :lessonId 
        AND ac.used = false 
        AND ac.expiry_date > NOW()
        ORDER BY ac.created_at DESC
        LIMIT 5
        """, nativeQuery = true)
    List<AccessCode> findRecentUnusedByLessonId(@Param("lessonId") String lessonId);

    @Query(value = """
        SELECT COUNT(*) > 0 FROM access_codes 
        WHERE lesson_id = :lessonId 
        AND used = false 
        AND expiry_date > NOW()
        """, nativeQuery = true)
    boolean hasValidCodesForLesson(@Param("lessonId") String lessonId);

    @Modifying
    @Query("UPDATE AccessCode ac SET ac.used = true, ac.usedAt = CURRENT_TIMESTAMP, ac.usedBy.id = :userId WHERE ac.code = :code")
    void markCodeAsUsed(@Param("code") String code, @Param("userId") String userId);
}