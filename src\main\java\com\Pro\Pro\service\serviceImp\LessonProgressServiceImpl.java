package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.Lesson;
import com.Pro.Pro.model.LessonProgressStatus;
import com.Pro.Pro.model.StudentLesson;
import com.Pro.Pro.repository.LessonRepository;
import com.Pro.Pro.repository.StudentLessonRepository;
import com.Pro.Pro.service.LessonProgressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class LessonProgressServiceImpl implements LessonProgressService  {

    private final StudentLessonRepository studentLessonRepository;
    private final LessonRepository lessonRepository;

    @Override
    public void validateLessonAccess(String studentId, String lessonId, LessonProgressService.LessonPart partToAccess) {

    }


    @Override
    @Transactional
    public void validateLessonAccess(String studentId, String lessonId, LessonPart partToAccess) {
        StudentLesson record = studentLessonRepository.findByStudentIdAndLessonId(studentId, lessonId)
                .orElseThrow(() -> new LessonNotPurchasedException("يجب عليك شراء هذا الدرس أولاً"));

        Lesson lesson = lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("الدرس غير موجود"));

        switch (partToAccess) {
            case EXAM:
                if (record.getProgressStatus() != LessonProgressStatus.PURCHASED) {
                    throw new IllegalStateException("لا يمكن الوصول إلى الامتحان إلا من حالة 'تم الشراء'");
                }
                break;

            case VIDEO:
                if (lesson.getExam() != null &&
                        record.getProgressStatus().ordinal() < LessonProgressStatus.EXAM_PASSED.ordinal()) {
                    throw new ExamNotPassedException(
                            "يجب عليك اجتياز الامتحان (درجة ≥ " + lesson.getExam().getPassingScore() +
                                    "%) قبل مشاهدة الفيديو");
                }
                break;

            case ASSIGNMENT:
                if (record.getProgressStatus().ordinal() < LessonProgressStatus.VIDEO_WATCHED.ordinal()) {
                    throw new VideoNotWatchedException(
                            "يجب عليك مشاهدة محتوى الفيديو قبل محاولة حل الواجبات");
                }
                break;

            default:
                throw new IllegalArgumentException("جزء الدرس المطلوب غير صحيح");
        }
    }

    @Override
    @Transactional
    public void updateProgress(String studentId, String lessonId, LessonProgressStatus newStatus) {
        try {
            StudentLesson record = studentLessonRepository.findByStudentIdAndLessonId(studentId, lessonId)
                    .orElseThrow(() -> new LessonNotPurchasedException("يجب عليك شراء هذا الدرس أولاً"));

            // Validate progress sequence
            if (newStatus.ordinal() <= record.getProgressStatus().ordinal()) {
                throw new IllegalStateException("لا يمكن التراجع في حالة التقدم");
            }

            // Validate specific transitions
            switch (newStatus) {
                case EXAM_PASSED:
                    if (record.getProgressStatus() != LessonProgressStatus.PURCHASED) {
                        throw new IllegalStateException("يمكن اجتياز الامتحان فقط من حالة 'تم الشراء'");
                    }
                    break;
                case VIDEO_WATCHED:
                    if (record.getProgressStatus().ordinal() < LessonProgressStatus.EXAM_PASSED.ordinal()) {
                        throw new IllegalStateException("يجب اجتياز الامتحان قبل مشاهدة الفيديو");
                    }
                    break;
                case ASSIGNMENT_DONE:
                    if (record.getProgressStatus().ordinal() < LessonProgressStatus.VIDEO_WATCHED.ordinal()) {
                        throw new IllegalStateException("يجب مشاهدة الفيديو قبل تقديم الواجب");
                    }
                    break;
            }

            record.setProgressStatus(newStatus);
            record.setLastUpdated(new Date());
            studentLessonRepository.save(record);
        } catch (LessonAccessException | IllegalStateException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("فشل في تحديث تقدم الدرس", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private String getAccessErrorMessage(String studentId, String lessonId, Lesson lesson) {
        if (lesson.getExam() == null) return null;

        StudentLesson record = studentLessonRepository.findByStudentIdAndLessonId(studentId, lessonId)
                .orElseThrow(() -> new LessonNotPurchasedException("You need to purchase this lesson first"));

        switch (record.getProgressStatus()) {
            case PURCHASED:
                return "You must complete and pass the exam to access this video content";
            case EXAM_PASSED:
                return "You must watch the video before accessing assignments";
            case VIDEO_WATCHED:
                return "You must complete the previous assignments";
            case ASSIGNMENT_DONE:
                return null; // Full access
            default:
                return "Access restricted - please complete previous requirements";
        }
    }

    public enum LessonPart {
        EXAM, VIDEO, ASSIGNMENT
    }
}