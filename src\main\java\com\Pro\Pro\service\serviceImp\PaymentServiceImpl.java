package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.LessonAccessRequest;
import com.Pro.Pro.dto.response.PaymentResponse;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.PaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    private static final int ACCESS_DURATION_DAYS = 14;
    private final LessonRepository lessonRepository;
    private final StudentLessonRepository studentLessonRepository;
    private final FawryPaymentRepository fawryPaymentRepository;
    private final AccessCodeRepository accessCodeRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public PaymentResponse grantLessonAccess(LessonAccessRequest request, String studentId) {
        try {
            validateRequest(request);
            Lesson lesson = getLesson(request.getLessonId());
            User student = getStudent(studentId);
            handleExistingAccess(studentId, lesson.getId());

            return request.getAccessCode() != null
                    ? processAccessCode(request.getAccessCode(), lesson, student)
                    : processPayment(request.getPaymentMethod(), lesson, student);
        } catch (BadRequestException | ConflictException | ResourceNotFoundException e) {
            log.error("Error in grantLessonAccess: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in grantLessonAccess", e);
            throw new ServiceException("Failed to grant lesson access: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public boolean hasValidAccess(String studentId, String lessonId) {
        try {
            return studentLessonRepository.existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
                    studentId, lessonId, new Date());
        } catch (Exception e) {
            log.error("Error checking valid access", e);
            throw new ServiceException("Failed to check access validity: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void grantAccess(User student, Lesson lesson, PaymentMethod method, String reference) {
        try {
            studentLessonRepository.deleteByStudentIdAndLessonId(student.getId(), lesson.getId());
            StudentLesson record = StudentLesson.builder()
                    .student(student)
                    .lesson(lesson)
                    .paymentMethod(method)
                    .paymentReference(reference)
                    .paymentDate(new Date())
                    .accessExpiryDate(calculateExpiryDate())
                    .completed(false)
                    .progressStatus(LessonProgressStatus.PURCHASED) // Initial status
                    .build();
            studentLessonRepository.save(record);
        } catch (Exception e) {
            throw new ServiceException("Failed to grant access: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public LocalDate getAccessExpiryDate(String studentId, String lessonId) {
        try {
            StudentLesson record = studentLessonRepository.findByStudentIdAndLessonId(studentId, lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("Access record not found"));
            return record.getAccessExpiryDate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        } catch (ResourceNotFoundException e) {
            log.error("Access record not found", e);
            throw e;
        } catch (Exception e) {
            log.error("Error getting access expiry date", e);
            throw new ServiceException("Failed to get access expiry date: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public String getPaymentStatus(String studentId, String lessonId) {
        try {
            Optional<FawryPayment> payment = fawryPaymentRepository
                    .findByStudent_IdAndLesson_Id(studentId, lessonId);
            return payment.map(p -> p.getStatus().name()).orElse(null);
        } catch (Exception e) {
            log.error("Error getting payment status", e);
            throw new ServiceException("Failed to get payment status: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // Private helper methods with proper error handling
    private void validateRequest(LessonAccessRequest request) {
        if (request.getAccessCode() == null && request.getPaymentMethod() == null) {
            throw new BadRequestException("Either access code or payment method must be provided");
        }
        if (request.getAccessCode() != null && request.getPaymentMethod() != null) {
            throw new BadRequestException("Cannot use both access code and payment method");
        }
    }

    private Lesson getLesson(String lessonId) {
        return lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("Lesson not found"));
    }

    private User getStudent(String studentId) {
        return userRepository.findById(studentId)
                .orElseThrow(() -> new ResourceNotFoundException("Student not found"));
    }

    private void handleExistingAccess(String studentId, String lessonId) {
        try {
            Optional<StudentLesson> existingAccess = studentLessonRepository
                    .findByStudentIdAndLessonId(studentId, lessonId);
            if (existingAccess.isPresent()) {
                StudentLesson access = existingAccess.get();
                if (access.getAccessExpiryDate().after(new Date())) {
                    throw new ConflictException("You already have active access to this lesson");
                }
                studentLessonRepository.delete(access);
            }
        } catch (Exception e) {
            throw new ServiceException("Error checking existing access: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private PaymentResponse processAccessCode(String code, Lesson lesson, User student) {
        try {
            AccessCode accessCode = accessCodeRepository.findByCodeAndLesson_Id(code, lesson.getId())
                    .orElseThrow(() -> new BadRequestException("Invalid access code"));

            if (accessCode.isUsed()) {
                throw new BadRequestException("This access code has already been used");
            }

            markCodeAsUsed(accessCode, student);
            grantAccess(student, lesson, PaymentMethod.CODE, "CODE_" + code);
            return buildSuccessResponse(lesson, "ACCESS_GRANTED");
        } catch (BadRequestException e) {
            log.error("Invalid access code: {}", code);
            throw e;
        } catch (Exception e) {
            throw new ServiceException("Failed to process access code: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void markCodeAsUsed(AccessCode accessCode, User student) {
        accessCode.setUsed(true);
        accessCode.setUsedBy(student);
        accessCode.setUsedAt(new Date());
        accessCodeRepository.save(accessCode);
    }

    private PaymentResponse processPayment(PaymentMethod method, Lesson lesson, User student) {
        try {
            if (method != PaymentMethod.FAWRY) {
                throw new BadRequestException("Unsupported payment method");
            }

            String reference = generatePaymentReference();
            createFawryPaymentRecord(lesson, student, reference);
            grantAccess(student, lesson, method, reference);
            return buildPaymentResponse(lesson, reference);
        } catch (BadRequestException e) {
            log.error("Invalid payment method: {}", method);
            throw e;
        } catch (Exception e) {
            throw new ServiceException("Failed to process payment: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private String generatePaymentReference() {
        return "FAWRY-" + UUID.randomUUID().toString().substring(0, 8);
    }

    private void createFawryPaymentRecord(Lesson lesson, User student, String reference) {
        FawryPayment payment = FawryPayment.builder()
                .student(student)
                .lesson(lesson)
                .referenceNumber(reference)
                .amount(lesson.getPrice())
                .status(PaymentStatus.PAID)
                .build();
        fawryPaymentRepository.save(payment);
    }

    private Date calculateExpiryDate() {
        return Date.from(LocalDate.now()
                .plusDays(ACCESS_DURATION_DAYS)
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant());
    }

    private PaymentResponse buildSuccessResponse(Lesson lesson, String status) {
        return PaymentResponse.builder()
                .status(status)
                .lessonId(lesson.getId())
                .lessonName(lesson.getName())
                .price(lesson.getPrice())
                .build();
    }

    private PaymentResponse buildPaymentResponse(Lesson lesson, String reference) {
        return PaymentResponse.builder()
                .status("PAYMENT_SUCCESS")
                .lessonId(lesson.getId())
                .lessonName(lesson.getName())
                .price(lesson.getPrice())
                .referenceNumber(reference)
                .paymentUrl(generateFawryPaymentUrl(reference))
                .build();
    }

    private String generateFawryPaymentUrl(String reference) {
        return "https://atfawry.fawrystaging.com/pay/" + reference;
    }
}