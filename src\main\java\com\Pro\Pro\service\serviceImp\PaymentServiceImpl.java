package com.Pro.Pro.service.serviceImp;

import com.Pro.Pro.dto.request.LessonAccessRequest;
import com.Pro.Pro.dto.response.PaymentResponse;
import com.Pro.Pro.exception.*;
import com.Pro.Pro.model.*;
import com.Pro.Pro.repository.*;
import com.Pro.Pro.service.PaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    // Configuration constants
    private static final int ACCESS_DURATION_DAYS = 14;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long PAYMENT_TIMEOUT_MINUTES = 30;
    private static final BigDecimal MIN_PAYMENT_AMOUNT = new BigDecimal("1.00");
    private static final BigDecimal MAX_PAYMENT_AMOUNT = new BigDecimal("10000.00");

    // Validation patterns
    private static final Pattern REFERENCE_PATTERN = Pattern.compile("^[A-Z0-9-]{8,20}$");
    private static final Pattern ACCESS_CODE_PATTERN = Pattern.compile("^[A-Z0-9]{6,12}$");

    // Fawry configuration
    @Value("${fawry.base.url:https://atfawry.fawrystaging.com}")
    private String fawryBaseUrl;

    @Value("${fawry.merchant.code:}")
    private String fawryMerchantCode;

    @Value("${fawry.security.key:}")
    private String fawrySecurityKey;

    // Thread-safe cache for payment processing
    private final Map<String, LocalDateTime> processingPayments = new ConcurrentHashMap<>();

    // Repository dependencies
    private final LessonRepository lessonRepository;
    private final StudentLessonRepository studentLessonRepository;
    private final FawryPaymentRepository fawryPaymentRepository;
    private final AccessCodeRepository accessCodeRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public PaymentResponse grantLessonAccess(LessonAccessRequest request, String studentId) {
        // التحقق من صحة الطلب أولاً
        if (request == null) {
            log.error("طلب منح الوصول فارغ");
            throw new BadRequestException("طلب منح الوصول مطلوب");
        }

        log.info("منح الوصول للدرس - الطالب: {}, الدرس: {}", studentId, request.getLessonId());

        try {
            // التحقق من صحة البيانات المدخلة
            validateLessonAccessRequest(request, studentId);

            // التحقق من عدم وجود عملية دفع قيد المعالجة
            checkConcurrentPaymentProcessing(studentId, request.getLessonId());

            Lesson lesson = getLesson(request.getLessonId());
            User student = getStudent(studentId);

            // التحقق من الوصول الحالي
            handleExistingAccess(studentId, lesson.getId());

            // معالجة الطلب حسب النوع
            PaymentResponse response = request.getAccessCode() != null
                    ? processAccessCode(request.getAccessCode(), lesson, student)
                    : processPayment(request.getPaymentMethod(), lesson, student);

            log.info("تم منح الوصول للدرس بنجاح - الطالب: {}, الدرس: {}", studentId, lesson.getId());
            return response;

        } catch (BadRequestException | ConflictException | ResourceNotFoundException e) {
            log.error("خطأ في منح الوصول للدرس: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("خطأ غير متوقع في منح الوصول للدرس", e);
            throw new ServiceException("فشل في منح الوصول للدرس: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            // إزالة قفل المعالجة
            clearPaymentProcessingLock(studentId, request.getLessonId());
        }
    }

    @Override
    public boolean hasValidAccess(String studentId, String lessonId) {
        log.debug("التحقق من صحة الوصول - الطالب: {}, الدرس: {}", studentId, lessonId);

        try {
            // التحقق من صحة المعرفات
            if (!StringUtils.hasText(studentId)) {
                throw new BadRequestException("معرف الطالب مطلوب");
            }

            if (!StringUtils.hasText(lessonId)) {
                throw new BadRequestException("معرف الدرس مطلوب");
            }

            // التحقق من وجود وصول صالح
            boolean hasAccess = studentLessonRepository.existsByStudentIdAndLessonIdAndAccessExpiryDateAfter(
                    studentId, lessonId, new Date());

            log.debug("نتيجة التحقق من الوصول: {} - الطالب: {}, الدرس: {}", hasAccess, studentId, lessonId);
            return hasAccess;

        } catch (BadRequestException e) {
            log.warn("خطأ في بيانات التحقق من الوصول: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("خطأ في التحقق من صحة الوصول", e);
            throw new ServiceException("فشل في التحقق من صحة الوصول: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    private void grantAccess(User student, Lesson lesson, PaymentMethod method, String reference) {
//        try {
//            studentLessonRepository.deleteByStudentIdAndLessonId(student.getId(), lesson.getId());
//            StudentLesson record = StudentLesson.builder()
//                    .student(student)
//                    .lesson(lesson)
//                    .paymentMethod(method)
//                    .paymentReference(reference)
//                    .paymentDate(new Date())
//                    .accessExpiryDate(calculateExpiryDate())
//                    .completed(false)
//                    .progressStatus(LessonProgressStatus.PURCHASED) // Initial status
//                    .build();
//            studentLessonRepository.save(record);
//        } catch (Exception e) {
//            throw new ServiceException("Failed to grant access: " + e.getMessage(),
//                    e, HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    @Override
    public LocalDate getAccessExpiryDate(String studentId, String lessonId) {
        try {
            StudentLesson record = studentLessonRepository.findByStudentIdAndLessonId(studentId, lessonId)
                    .orElseThrow(() -> new ResourceNotFoundException("Access record not found"));
            return record.getAccessExpiryDate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        } catch (ResourceNotFoundException e) {
            log.error("Access record not found", e);
            throw e;
        } catch (Exception e) {
            log.error("Error getting access expiry date", e);
            throw new ServiceException("Failed to get access expiry date: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public String getPaymentStatus(String studentId, String lessonId) {
        try {
            Optional<FawryPayment> payment = fawryPaymentRepository
                    .findByStudent_IdAndLesson_Id(studentId, lessonId);
            return payment.map(p -> p.getStatus().name()).orElse(null);
        } catch (Exception e) {
            log.error("Error getting payment status", e);
            throw new ServiceException("Failed to get payment status: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    // =============== Validation Methods ===============
    /**
     * التحقق من صحة طلب الوصول للدرس
     */
    private void validateLessonAccessRequest(LessonAccessRequest request, String studentId) {
        // التحقق من وجود الطلب
        if (request == null) {
            throw new BadRequestException("طلب الوصول للدرس مطلوب");
        }

        // التحقق من معرف الطالب
        if (!StringUtils.hasText(studentId)) {
            throw new BadRequestException("معرف الطالب مطلوب");
        }

        // التحقق من معرف الدرس
        if (!StringUtils.hasText(request.getLessonId())) {
            throw new BadRequestException("معرف الدرس مطلوب");
        }

        // التحقق من وجود إما كود الوصول أو طريقة الدفع
        boolean hasAccessCode = StringUtils.hasText(request.getAccessCode());
        boolean hasPaymentMethod = request.getPaymentMethod() != null;

        if (!hasAccessCode && !hasPaymentMethod) {
            throw new BadRequestException("يجب توفير إما كود الوصول أو طريقة الدفع");
        }

        if (hasAccessCode && hasPaymentMethod) {
            throw new BadRequestException("لا يمكن استخدام كود الوصول وطريقة الدفع معاً");
        }

        // التحقق من صحة كود الوصول
        if (hasAccessCode) {
            validateAccessCode(request.getAccessCode());
        }

        // التحقق من صحة طريقة الدفع
        if (hasPaymentMethod) {
            validatePaymentMethod(request.getPaymentMethod());
        }
    }

    /**
     * التحقق من صحة كود الوصول
     */
    private void validateAccessCode(String accessCode) {
        if (!StringUtils.hasText(accessCode)) {
            throw new BadRequestException("كود الوصول لا يمكن أن يكون فارغاً");
        }

        if (accessCode.length() < 6 || accessCode.length() > 12) {
            throw new BadRequestException("كود الوصول يجب أن يكون بين 6 و 12 حرف");
        }

        if (!ACCESS_CODE_PATTERN.matcher(accessCode.toUpperCase()).matches()) {
            throw new BadRequestException("كود الوصول يجب أن يحتوي على أحرف وأرقام إنجليزية فقط");
        }
    }

    /**
     * التحقق من صحة طريقة الدفع
     */
    private void validatePaymentMethod(PaymentMethod paymentMethod) {
        if (paymentMethod == null) {
            throw new BadRequestException("طريقة الدفع مطلوبة");
        }

        if (paymentMethod != PaymentMethod.FAWRY) {
            throw new BadRequestException("طريقة الدفع غير مدعومة. يُدعم فقط الدفع عبر فوري");
        }
    }

    /**
     * التحقق من صحة مبلغ الدفع
     */
    private void validatePaymentAmount(BigDecimal amount) {
        if (amount == null) {
            throw new BadRequestException("مبلغ الدفع مطلوب");
        }

        if (amount.compareTo(MIN_PAYMENT_AMOUNT) < 0) {
            throw new BadRequestException("مبلغ الدفع يجب أن يكون " + MIN_PAYMENT_AMOUNT + " جنيه على الأقل");
        }

        if (amount.compareTo(MAX_PAYMENT_AMOUNT) > 0) {
            throw new BadRequestException("مبلغ الدفع يجب أن يكون أقل من " + MAX_PAYMENT_AMOUNT + " جنيه");
        }

        // التحقق من عدد المنازل العشرية
        if (amount.scale() > 2) {
            throw new BadRequestException("مبلغ الدفع يجب أن يحتوي على منزلتين عشريتين كحد أقصى");
        }
    }

    /**
     * التحقق من صحة مرجع الدفع
     */
    private void validatePaymentReference(String reference) {
        if (!StringUtils.hasText(reference)) {
            throw new BadRequestException("مرجع الدفع مطلوب");
        }

        if (!REFERENCE_PATTERN.matcher(reference).matches()) {
            throw new BadRequestException("مرجع الدفع غير صحيح");
        }
    }

    // =============== Helper Methods ===============
    /**
     * التحقق من عدم وجود عملية دفع قيد المعالجة
     */
    private void checkConcurrentPaymentProcessing(String studentId, String lessonId) {
        String key = studentId + ":" + lessonId;
        LocalDateTime processingTime = processingPayments.get(key);

        if (processingTime != null) {
            // التحقق من انتهاء مهلة المعالجة
            if (processingTime.plusMinutes(PAYMENT_TIMEOUT_MINUTES).isAfter(LocalDateTime.now())) {
                throw new ConflictException("يوجد طلب دفع قيد المعالجة لهذا الدرس. يرجى المحاولة لاحقاً");
            } else {
                // إزالة القفل المنتهي الصلاحية
                processingPayments.remove(key);
            }
        }

        // إضافة قفل جديد
        processingPayments.put(key, LocalDateTime.now());
    }

    /**
     * إزالة قفل معالجة الدفع
     */
    private void clearPaymentProcessingLock(String studentId, String lessonId) {
        String key = studentId + ":" + lessonId;
        processingPayments.remove(key);
    }

    /**
     * الحصول على الدرس مع التحقق من الوجود
     */
    private Lesson getLesson(String lessonId) {
        return lessonRepository.findById(lessonId)
                .orElseThrow(() -> new ResourceNotFoundException("الدرس غير موجود بالمعرف: " + lessonId));
    }

    /**
     * الحصول على الطالب مع التحقق من الوجود
     */
    private User getStudent(String studentId) {
        User student = userRepository.findById(studentId)
                .orElseThrow(() -> new ResourceNotFoundException("الطالب غير موجود بالمعرف: " + studentId));

        // التحقق من أن المستخدم طالب
        if (student.getRole() != Role.STUDENT) {
            throw new BadRequestException("المستخدم ليس طالباً");
        }

        return student;
    }

    /**
     * التحقق من الوصول الحالي ومعالجته
     */
    private void handleExistingAccess(String studentId, String lessonId) {
        try {
            Optional<StudentLesson> existingAccess = studentLessonRepository
                    .findByStudentIdAndLessonId(studentId, lessonId);

            if (existingAccess.isPresent()) {
                StudentLesson access = existingAccess.get();

                // التحقق من صلاحية الوصول الحالي
                if (access.getAccessExpiryDate() != null && access.getAccessExpiryDate().after(new Date())) {
                    throw new ConflictException("لديك وصول صالح لهذا الدرس حتى تاريخ: " + access.getAccessExpiryDate());
                }

                // حذف الوصول المنتهي الصلاحية
                studentLessonRepository.delete(access);
                log.info("تم حذف الوصول المنتهي الصلاحية - الطالب: {}, الدرس: {}", studentId, lessonId);
            }
        } catch (ConflictException e) {
            throw e;
        } catch (Exception e) {
            log.error("خطأ في التحقق من الوصول الحالي", e);
            throw new ServiceException("خطأ في التحقق من الوصول الحالي: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * منح الوصول للطالب
     */
    private void grantAccess(User student, Lesson lesson, PaymentMethod method, String reference) {
        try {
            // Always validate payment amount (for both payments and access codes)
            validatePaymentAmount(lesson.getPrice());

            // Only validate payment reference if it's a payment (not an access code)
            if (method != PaymentMethod.CODE) {
                validatePaymentReference(reference);
            }

            // حذف أي وصول سابق (احتياطي)
            studentLessonRepository.deleteByStudentIdAndLessonId(student.getId(), lesson.getId());

            // إنشاء سجل وصول جديد
            StudentLesson record = StudentLesson.builder()
                    .student(student)
                    .lesson(lesson)
                    .paymentMethod(method)
                    .paymentReference(reference)
                    .paymentDate(new Date())
                    .accessExpiryDate(calculateExpiryDate())
                    .completed(false)
                    .progressStatus(LessonProgressStatus.PURCHASED)
                    .build();

            studentLessonRepository.save(record);
            log.info("تم منح الوصول بنجاح - الطالب: {}, الدرس: {}, المرجع: {}",
                    student.getId(), lesson.getId(), reference);

        } catch (Exception e) {
            log.error("فشل في منح الوصول", e);
            throw new ServiceException("فشل في منح الوصول: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
//    private void handleExistingAccess(String studentId, String lessonId) {
//        try {
//            Optional<StudentLesson> existingAccess = studentLessonRepository
//                    .findByStudentIdAndLessonId(studentId, lessonId);
//            if (existingAccess.isPresent()) {
//                StudentLesson access = existingAccess.get();
//                if (access.getAccessExpiryDate().after(new Date())) {
//                    throw new ConflictException("You already have active access to this lesson");
//                }
//                studentLessonRepository.delete(access);
//            }
//        } catch (Exception e) {
//            throw new ServiceException("Error checking existing access: " + e.getMessage(),
//                    e, HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    private PaymentResponse processAccessCode(String code, Lesson lesson, User student) {
        try {
            AccessCode accessCode = accessCodeRepository.findByCodeAndLesson_Id(code, lesson.getId())
                    .orElseThrow(() -> new BadRequestException("Invalid access code"));

            if (accessCode.isUsed()) {
                throw new BadRequestException("This access code has already been used");
            }

            markCodeAsUsed(accessCode, student);
            // Don't validate payment reference for access codes
            grantAccessWithoutReferenceValidation(student, lesson, PaymentMethod.CODE, "CODE_" + code);
            return buildSuccessResponse(lesson, "ACCESS_GRANTED");
        } catch (BadRequestException e) {
            log.error("Invalid access code: {}", code);
            throw e;
        } catch (Exception e) {
            throw new ServiceException("Failed to process access code: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void grantAccessWithoutReferenceValidation(User student, Lesson lesson, PaymentMethod method, String reference) {
        try {
            // Skip payment reference validation for access codes
            if (method != PaymentMethod.CODE) {
                validatePaymentAmount(lesson.getPrice());
                validatePaymentReference(reference);
            }

            // Delete any previous access
            studentLessonRepository.deleteByStudentIdAndLessonId(student.getId(), lesson.getId());

            // Create new access record
            StudentLesson record = StudentLesson.builder()
                    .student(student)
                    .lesson(lesson)
                    .paymentMethod(method)
                    .paymentReference(reference)
                    .paymentDate(new Date())
                    .accessExpiryDate(calculateExpiryDate())
                    .completed(false)
                    .progressStatus(LessonProgressStatus.PURCHASED)
                    .build();

            studentLessonRepository.save(record);
            log.info("تم منح الوصول بنجاح - الطالب: {}, الدرس: {}, المرجع: {}",
                    student.getId(), lesson.getId(), reference);

        } catch (Exception e) {
            log.error("فشل في منح الوصول", e);
            throw new ServiceException("فشل في منح الوصول: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void markCodeAsUsed(AccessCode accessCode, User student) {
        accessCode.setUsed(true);
        accessCode.setUsedBy(student);
        accessCode.setUsedAt(new Date());
        accessCodeRepository.save(accessCode);
    }

    private PaymentResponse processPayment(PaymentMethod method, Lesson lesson, User student) {
        try {
            if (method != PaymentMethod.FAWRY) {
                throw new BadRequestException("Unsupported payment method");
            }

            String reference = generatePaymentReference();
            createFawryPaymentRecord(lesson, student, reference);
            grantAccess(student, lesson, method, reference);
            return buildPaymentResponse(lesson, reference);
        } catch (BadRequestException e) {
            log.error("Invalid payment method: {}", method);
            throw e;
        } catch (Exception e) {
            throw new ServiceException("Failed to process payment: " + e.getMessage(),
                    e, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private String generatePaymentReference() {
        return "FAWRY-" + UUID.randomUUID().toString().substring(0, 8);
    }

    private void createFawryPaymentRecord(Lesson lesson, User student, String reference) {
        FawryPayment payment = FawryPayment.builder()
                .student(student)
                .lesson(lesson)
                .referenceNumber(reference)
                .amount(lesson.getPrice())
                .status(PaymentStatus.PAID)
                .build();
        fawryPaymentRepository.save(payment);
    }

    private Date calculateExpiryDate() {
        return Date.from(LocalDate.now()
                .plusDays(ACCESS_DURATION_DAYS)
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant());
    }

    private PaymentResponse buildSuccessResponse(Lesson lesson, String status) {
        return PaymentResponse.builder()
                .status(status)
                .lessonId(lesson.getId())
                .lessonName(lesson.getName())
                .price(lesson.getPrice())
                .build();
    }

    private PaymentResponse buildPaymentResponse(Lesson lesson, String reference) {
        return PaymentResponse.builder()
                .status("PAYMENT_SUCCESS")
                .lessonId(lesson.getId())
                .lessonName(lesson.getName())
                .price(lesson.getPrice())
                .referenceNumber(reference)
                .paymentUrl(generateFawryPaymentUrl(reference))
                .build();
    }

    private String generateFawryPaymentUrl(String reference) {
        return "https://atfawry.fawrystaging.com/pay/" + reference;
    }
}
