package com.Pro.Pro.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.math.BigDecimal;

@Entity
@Table(name = "student_exam_answers")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentExamAnswer {
    @Id
    @GeneratedValue(generator = "nanoid")
    @GenericGenerator(name = "nanoid", strategy = "com.Pro.Pro.NanoIdGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private String id;

    @ManyToOne
    @JoinColumn(name = "submission_id")
    private ExamSubmission submission;

    @ManyToOne
    @JoinColumn(name = "question_id")
    private ExamQuestion question;

    private String answerText;
    private boolean correct;
    private BigDecimal pointsEarned;
}